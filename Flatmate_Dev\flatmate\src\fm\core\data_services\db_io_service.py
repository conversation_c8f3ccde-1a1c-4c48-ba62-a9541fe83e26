"""
Enhanced Database I/O Service - High-level API for database operations.

This module provides a unified interface for database operations including:
- Import/export functionality (CSV, DataFrame)
- Query helpers for common operations
- Statistics and reporting functions
- Column management integration

It serves as the primary entry point for all database interactions,
wrapping the lower-level repository classes and using the ColumnManager
for all column-related operations.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple

import pandas as pd

from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository
from fm.core.database.sql_repository.transaction_repository import ImportResult, Transaction
from .column_manager import ColumnManager
from .utils.standard_columns import StandardColumns, ColumnUsage


class DBIOService:
    """
    High-level service for database I/O operations.
    
    This class provides a unified API for all database operations,
    including import/export, queries, and statistics. It integrates
    with the ColumnManager for all column-related operations.
    """
    
    def __init__(
        self, 
        repo: Optional[SQLiteTransactionRepository] = None,
        column_manager: Optional[ColumnManager] = None
    ):
        """
        Initialize the DB I/O Service.
        
        Args:
            repo: Optional repository instance for dependency injection.
                 If None, a new SQLiteTransactionRepository will be created.
            column_manager: Optional ColumnManager instance for dependency injection.
                 If None, a new ColumnManager will be created.
        """
        self.repo = repo or SQLiteTransactionRepository()
        self.column_manager = column_manager or ColumnManager()
    
    # --- Import/export (df) --------------------------------------------------
    
    #---import transactions (df) 
    def update_database(
        self, 
        df: pd.DataFrame, 
        source_file: Optional[str] = None
    ) -> ImportResult:
        """
        Import transactions from a pandas DataFrame.
        
        Args:
            df: DataFrame containing transaction data
            source_file: Optional source file path for reference
            
        Returns:
            ImportResult with counts of added, duplicate, and error records
        """
        if df is None or df.empty:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=0,
                errors=["No data to import"]
            )
        
        try:
            # Convert display column names to database column names if needed
            df_columns = set(df.columns)
            display_names = {col.value.display_name for col in StandardColumns}
            
            # If DataFrame has display column names, convert to database column names
            if any(col in display_names for col in df_columns):
                df = self._convert_display_to_db_columns(df)
            
            # Add source_file information if provided
            if source_file:
                source_file_col = StandardColumns.SOURCE_FILENAME.db_name
                df[source_file_col] = source_file
            
            # Delegate to repository
            return self.repo.add_transactions_from_df(df, source_file=source_file)
        except Exception as e:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=len(df),
                errors=[f"Error importing DataFrame: {str(e)}"]
            )
    
     # --- Export transactions (df)
   
    def get_transactions(
        self, 
        only_columns_with_data: bool = True,
        use_display_names: bool = True,
        **filters
    ) -> pd.DataFrame:
        """
        Get transactions as a DataFrame with optional filtering and formatting.
        
        This is the primary method for retrieving transaction data from the database.
        It supports flexible filtering and column name formatting.
        
        Args:
            only_columns_with_data: If True, only include columns that have data
            use_display_names: If True, use display names for columns instead of database names
            **filters: Optional filters to apply to the query. Common filters include:
                - start_date (datetime): Filter transactions on or after this date
                - end_date (datetime): Filter transactions on or before this date
                - account_number (str): Filter by account number
                - min_amount (float): Filter by minimum transaction amount
                - max_amount (float): Filter by maximum transaction amount
                - tags (str): Filter by tag (comma-separated tags)
                - description (str): Filter by description (partial match)
                - Any column name from StandardColumns can be used as a filter
            
        Returns:
            DataFrame containing transaction data with requested formatting
            
        Example:
            # Get all transactions from January 2023 for a specific account
            df = service.get_transactions(
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2023, 1, 31),
                account_number="12345"
            )
        """
        # Get data with all columns
        df = self._fetch_raw_transactions_df(all_cols=True, only_columns_with_data=only_columns_with_data, **filters)
        
        if df.empty:
            return df
        
        # Convert to display names if requested
        if use_display_names:
            df = self.column_manager.get_df_with_display_columns(df, only_columns_with_data=only_columns_with_data)
        
        return df

    def _fetch_raw_transactions_df(
            self, 
            *,  
            all_cols: bool = False, 
            only_columns_with_data: bool = True,
            **filters
        ) -> pd.DataFrame:
            """
            Internal method to fetch raw transactions as a DataFrame with database column names.
            This is used internally by get_transactions which provides a more user-friendly interface.
            
            Args:
                all_cols: If True, include all columns; otherwise, include only core columns
                only_columns_with_data: If True, only include columns that have data
                **filters: Optional filters to apply to the query. Common filters include:
                    - start_date (datetime): Filter transactions on or after this date
                    - end_date (datetime): Filter transactions on or before this date
                    - account_number (str): Filter by account number
                    - min_amount (float): Filter by minimum transaction amount
                    - max_amount (float): Filter by maximum transaction amount
                    - tags (str): Filter by tag (comma-separated tags)
                    - description (str): Filter by description (partial match)
                    - Any column name from StandardColumns can be used as a filter
                
            Returns:
                DataFrame containing transaction data with database column names
            """
            # Get transactions based on filters
            transactions = self.list_transactions(**filters)
            
            if not transactions:
                return pd.DataFrame()
            
            # Convert transactions to DataFrame
            df = pd.DataFrame([t.to_dict() for t in transactions])
            
            # Filter columns based on parameters
            if not all_cols:
                # Use only core display columns
                core_columns = [col.db_name for col in self.column_manager.get_columns_by_usage(ColumnUsage.DISPLAY.value)]
                # Filter to columns that exist in the DataFrame
                core_columns = [col for col in core_columns if col in df.columns]
                df = df[core_columns]
            
            # Filter to only columns with data if requested
            if only_columns_with_data:
                # Get columns that have at least one non-null value
                cols_with_data = [col for col in df.columns if not df[col].isna().all()]
                df = df[cols_with_data]
            
            return df
    
    # --- Debugging and testing ------------------------------------
    
    def dump_raw_db_content(
        self, 
        format: str = 'dataframe',
        file_path: Optional[Path] = None
    ) -> Union[pd.DataFrame, Dict, str, None]:
        """
        Dump raw database content for debugging and testing purposes.
        
        Args:
            format: Output format ('dataframe', 'json', 'csv')
            file_path: Optional file path to save the output
            
        Returns:
            Raw database content in the specified format, or None if saved to file
        """
        # Get all transactions as a DataFrame
        transactions = self.list_transactions()
        if not transactions:
            return pd.DataFrame() if format == 'dataframe' else {} if format == 'json' else ''
        
        # Convert to DataFrame
        df = pd.DataFrame([t.to_dict() for t in transactions])
        
        # Handle different output formats
        if format == 'dataframe':
            result = df
        elif format == 'json':
            result = df.to_json(orient='records')
        elif format == 'csv':
            result = df.to_csv(index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        # Save to file if requested
        if file_path:
            if format == 'json':
                df.to_json(file_path, orient='records')
            elif format == 'csv':
                df.to_csv(file_path, index=False)
            elif format == 'dataframe':
                df.to_pickle(file_path)
            return None
        
        return result
    
   

    # * ----------------- internal column conversion Method?-----------------------------------

    def _convert_display_to_db_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert DataFrame columns from display names to database names.
        
        Args:
            df: DataFrame with display column names
            
        Returns:
            DataFrame with database column names
        """
        # Create mapping of display names to database names
        display_to_db = {}
        for col in StandardColumns:
            display_to_db[col.value.display_name] = col.db_name
        
        # Rename columns that match display names
        rename_cols = {}
        for col in df.columns:
            if col in display_to_db:
                rename_cols[col] = display_to_db[col]
        
        if rename_cols:
            df = df.rename(columns=rename_cols)
        
        return df
    
   
  
    
    # --- Query helpers ---------------------------------------------
    
    def list_transactions(self, **filters) -> List[Transaction]:
        """
        List transactions with optional filtering.
        
        Args:
            **filters: Optional filters (start_date, end_date, account_number, etc.)
            
        Returns:
            List of Transaction objects matching the filters
        """
        return self.repo.get_transactions(filters)
    
    def get_transaction_by_id(self, transaction_id: int) -> Optional[Transaction]:
        """
        Get a transaction by its ID.
        
        Args:
            transaction_id: ID of the transaction to retrieve
            
        Returns:
            Transaction object if found, None otherwise
        """
        return self.repo.get_transaction_by_id(transaction_id)
    
    def update_transaction(self, transaction_id: int, updates: Dict[str, Any]) -> bool:
       # ! (core transaction data IS NOT EDITABLE, what is this funciton for exaclty!?) 
        """
        Update a transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            updates: Dictionary of fields to update
            
        Returns:
            True if successful, False otherwise
        """
        # Convert any display column names to database column names
        db_updates = {}
        for key, value in updates.items():
            col = StandardColumns.from_display_name(key)
            if col:
                db_updates[col.db_name] = value
            else:
                db_updates[key] = value
        
        return self.repo.update_transaction(transaction_id, db_updates)
    
    def update_transaction_tags(self, transaction_id: int, tags: str) -> bool:
        """
        Update the tags for a transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            tags: New tags string (comma-separated)
            
        Returns:
            True if successful, False otherwise
        """
        return self.update_transaction(transaction_id, {StandardColumns.TAGS.db_name: tags})
    
    def get_unique_account_numbers(self) -> List[str]:
        # ? Actually potentially usefull, however filtering is primarily done in the module
        """
        Get a list of unique account numbers in the database.
        
        Returns:
            List of unique account numbers
        """
        transactions = self.list_transactions()
        account_numbers = set()
        
        account_col = StandardColumns.ACCOUNT.db_name
        for transaction in transactions:
            account = getattr(transaction, account_col, None)
            if account:
                account_numbers.add(account)
        
        return sorted(list(account_numbers))
    
    # --- Statistics and reporting ---------------------------------
    
    def get_db_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the transaction database.
        
        Returns:
            Dictionary with statistics:
            - total_count: Total number of transactions
            - date_range: (min_date, max_date) tuple
            - account_counts: Dictionary mapping account numbers to transaction counts
            - total_amount: Sum of all transaction amounts
        """
        # Get all transactions from the database
        transactions = self.list_transactions()
        
        # If there are no transactions, return empty stats
        if not transactions:
            return {
                'total_count': 0,
                'date_range': (None, None),
                'account_counts': {},
                'total_amount': 0.0
            }
            
        # Continue with stats calculation since we have transactions
        
        # Get column names
        date_col = StandardColumns.DATE.db_name
        account_col = StandardColumns.ACCOUNT.db_name
        amount_col = StandardColumns.AMOUNT.db_name
        
        # Calculate statistics
        dates = [getattr(t, date_col) for t in transactions if hasattr(t, date_col) and getattr(t, date_col)]
        min_date = min(dates) if dates else None
        max_date = max(dates) if dates else None
        
        account_counts = {}
        for t in transactions:
            account = getattr(t, account_col, None)
            if account:
                account_counts[account] = account_counts.get(account, 0) + 1
        
        total_amount = sum(getattr(t, amount_col, 0) for t in transactions)
        
        return {
            'total_count': len(transactions),
            'date_range': (min_date, max_date),
            'account_counts': account_counts,
            'total_amount': total_amount
        }
    
    def delete_all_transactions(self) -> int:
        """
        Delete all transactions from the database.
        
        Returns:
            The number of transactions deleted.
        """
        return self.repo.delete_all_transactions()
    
  
  

# ******************************************************************************
#------------ # ! not currently implemented: 
      # NOTE:  * SPECULATIVE import/export  CSV io possibly useful but not implemented *
#--------Import_Export (CSV)------------------------
def import_csv(
        self, 
        file_path: Path, 
        mapping: Optional[Dict[str, str]] = None
    ) -> ImportResult:
        """
        Import transactions from a CSV file.
        
        Args:
            file_path: Path to the CSV file
            mapping: Optional column mapping (source_column -> target_column)
            
        Returns:
            ImportResult with counts of added, duplicate, and error records
        """
        try:
            # Read CSV into DataFrame
            df = pd.read_csv(file_path)
            
            # Apply column mapping if provided
            if mapping:
                df = df.rename(columns=mapping)
            
            # Import from DataFrame
            return self.import_dataframe(df, source_file=str(file_path))
        except Exception as e:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=1,
                errors=[f"Error importing CSV: {str(e)}"]
            )
    
def export_csv(
        self, 
        target: Path, 
        *, 
        all_cols: bool = False,
        only_columns_with_data: bool = True,
        use_display_names: bool = True,
        **kwargs
    ) -> None:
        """
        Export transactions to a CSV file.
        
        Args:
            target: Path to the output CSV file
            all_cols: If True, include all columns; otherwise, include only core columns
            only_columns_with_data: If True, only include columns that have data
            use_display_names: If True, use display names for columns; otherwise, use database names
            **kwargs: Additional arguments to pass to pandas to_csv
            
        Returns:
            None
        """
        # Get data as DataFrame
        df = self.export_dataframe(all_cols=all_cols, only_columns_with_data=only_columns_with_data)
        
        if df.empty:
            # Create empty file
            with open(target, 'w') as f:
                f.write('')
        else:
            # Convert to display names if requested
            if use_display_names:
                df = self.column_manager.get_df_with_display_columns(df, only_columns_with_data=only_columns_with_data)
            
            # Export with provided options
            df.to_csv(target, index=False, **kwargs)