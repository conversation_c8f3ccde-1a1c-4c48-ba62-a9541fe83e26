Current Database Module Structure
Your current database module has these main components:

Transaction Model: Defines what a transaction looks like in your application
Repository Interface: Defines methods for storing and retrieving transactions
SQLite Implementation: Implements the repository interface using SQLite
Data Service: Provides a higher-level interface for the application to use
This follows good architectural principles with clear separation of concerns, but as we've identified, it lacks a single source of truth for column names.

Proposed Improved Structure
Here's how we could restructure the database module to use a single source of truth:

1. FmColumnFormat as the Single Source of Truth
The FmColumnFormat enum should define ALL column names used throughout the system:

CSV import/export
Database tables
Transaction model attributes
2. Dynamic Database Schema Generation
We could create a system where the database schema is generated dynamically from FmColumnFormat:

python
CopyInsert
def _ensure_db_exists(self):
    """Ensure the database file and tables exist."""
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    # Get column definitions from FmColumnFormat
    from fm.modules.update_data.utils.statement_handlers.standards.fm_column_format import FmColumnFormat
    
    # Define required columns and their SQL types
    required_columns = {
        FmColumnFormat.DATE.value: "TEXT NOT NULL",
        FmColumnFormat.DETAILS.value: "TEXT NOT NULL",
        FmColumnFormat.AMOUNT.value: "REAL NOT NULL",
        # Add other required columns...
    }
    
    # Define optional columns
    optional_columns = {
        FmColumnFormat.BALANCE.value: "REAL",
        FmColumnFormat.CATEGORY.value: "TEXT",
        # Add other optional columns...
    }
    
    # Combine all columns
    all_columns = {**required_columns, **optional_columns}
    
    # Add system columns not in FmColumnFormat
    system_columns = {
        "id": "INTEGER PRIMARY KEY",
        "import_date": "TEXT",
        "modified_date": "TEXT",
        "is_deleted": "INTEGER DEFAULT 0"
    }
    
    # Build the CREATE TABLE statement
    columns_sql = ", ".join([f"{name} {type_}" for name, type_ in {**all_columns, **system_columns}.items()])
    create_table_sql = f"CREATE TABLE IF NOT EXISTS transactions ({columns_sql})"
    
    with self._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(create_table_sql)
        # Create indexes...
3. Configuration for Additional Fields
For additional database-specific fields, we could use a configuration file:

python
CopyInsert
# config/database_config.yaml
additional_columns:
  - name: custom_field1
    type: TEXT
    description: Custom field for special use
  - name: custom_field2
    type: REAL
    description: Another custom field
These could be loaded and added to the schema dynamically.

4. Transaction Model with Dynamic Attributes
The Transaction model could be updated to handle any columns defined in FmColumnFormat:

python
CopyInsert
class Transaction:
    def __init__(self, **kwargs):
        # Set default values for all known fields
        for field in FmColumnFormat:
            setattr(self, self._to_attribute_name(field.value), None)
        
        # Set provided values
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @staticmethod
    def _to_attribute_name(column_name):
        """Convert column name to valid Python attribute name."""
        return column_name.lower().replace(' ', '_')
Benefits of This Approach
Single Source of Truth: FmColumnFormat defines all column names
Flexibility: Easy to add new columns by updating FmColumnFormat
Consistency: Same column names used throughout the system
Clean Architecture: Maintains separation of concerns while ensuring consistency
This approach aligns with your preference for clean architectural boundaries and well-defined interfaces, while ensuring there's only one source of truth for column names.