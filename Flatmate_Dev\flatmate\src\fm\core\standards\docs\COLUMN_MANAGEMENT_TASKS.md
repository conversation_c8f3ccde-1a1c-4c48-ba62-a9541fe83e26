# Column Management Implementation - Agile Task List

## Epic: Column Management System Refactoring

**Goal**: Create a centralized, flexible column management system that serves as a single source of truth for all column definitions and operations across the application.

## Sprint 1: Foundation & Analysis

### User Story 1: Column Definition Analysis
**As a** developer  
**I want to** understand the current column usage across the application  
**So that** I can design an appropriate column management system

#### Tasks:
- [ ] Audit current column definitions in `fm_standard_columns.py`
- [ ] Identify all modules that reference or duplicate column definitions
- [ ] Document column usage patterns (display, source, categorization, system)
- [ ] Map dependencies between column-related modules
- [ ] Create test cases for current column behavior (for regression testing)

**Acceptance Criteria:**
- Complete audit document with findings
- Column usage patterns clearly identified
- Test cases covering current functionality

### User Story 2: Enhanced Column Metadata Design
**As a** developer  
**I want to** design an enhanced column metadata structure  
**So that** columns can have rich metadata and belong to multiple usage groups

#### Tasks:
- [ ] Create `ColumnMetadata` dataclass with `used_in` field
- [ ] Define `ColumnUsage` enum with SOURCE, DISPLAY, CATEGORIZE, SYSTEM categories
- [ ] Update `StandardColumns` to use the new metadata structure
- [ ] Add helper methods for column filtering by usage
- [ ] Write unit tests for the new structure

**Acceptance Criteria:**
- Column metadata includes usage groups, display properties, and descriptions
- Columns can belong to multiple usage groups
- Helper methods correctly filter columns by usage
- All tests pass

## Sprint 2: Core Implementation

### User Story 3: Column Manager Service
**As a** developer  
**I want to** implement a centralized ColumnManager service  
**So that** all column operations are handled consistently

#### Tasks:
- [ ] Create `ColumnManager` class with core functionality
- [ ] Implement column conversion methods (display ↔ db)
- [ ] Add methods to filter columns by usage
- [ ] Implement `get_display_columns_with_data()` for conditional display
- [ ] Add user preference handling
- [ ] Write comprehensive unit tests

**Acceptance Criteria:**
- ColumnManager correctly handles all column operations
- Columns can be filtered by usage and data presence
- User preferences are correctly applied
- All tests pass

### User Story 4: DBIOService Integration
**As a** developer  
**I want to** update DBIOService to use the new ColumnManager  
**So that** database operations use the canonical column definitions

#### Tasks:
- [ ] Refactor DBIOService to use ColumnManager for column operations
- [ ] Update column format conversion to use ColumnManager
- [ ] Add support for filtering columns by data presence
- [ ] Implement `dump_raw_db_content()` method for debugging
- [ ] Update existing tests and add new ones

**Acceptance Criteria:**
- DBIOService uses ColumnManager for all column operations
- Column filtering by data presence works correctly
- Raw DB content can be dumped for debugging
- All tests pass

## Sprint 3: Integration & Consolidation

### User Story 5: Module Consolidation
**As a** developer  
**I want to** consolidate all data-related services into a unified structure  
**So that** dependencies are clear and code organization is improved

#### Tasks:
- [ ] Create new module structure under `core.data_services`
- [ ] Move column-related code to appropriate modules
- [ ] Update imports across the codebase
- [ ] Ensure backward compatibility
- [ ] Update documentation

**Acceptance Criteria:**
- All data-related services are in a logical structure
- Imports are updated across the codebase
- No regression in functionality
- Documentation reflects the new structure

### User Story 6: User Preference Integration
**As a** user  
**I want to** customize how columns are displayed  
**So that** I can view data in a format that makes sense to me

#### Tasks:
- [ ] Implement user preference storage
- [ ] Add UI for customizing column display names
- [ ] Add UI for setting column order
- [ ] Integrate preferences with ColumnManager
- [ ] Add persistence for user preferences

**Acceptance Criteria:**
- Users can customize column display names
- Users can set column order
- Preferences are persisted between sessions
- Changes are immediately reflected in the UI

## Sprint 4: Testing & Documentation

### User Story 7: Comprehensive Testing
**As a** developer  
**I want to** ensure the column management system is thoroughly tested  
**So that** it works reliably in all scenarios

#### Tasks:
- [ ] Write integration tests for the complete system
- [ ] Test with various data scenarios (empty columns, all columns filled, etc.)
- [ ] Test user preference handling
- [ ] Test performance with large datasets
- [ ] Fix any issues found during testing

**Acceptance Criteria:**
- Test coverage > 90% for new code
- All edge cases are handled correctly
- Performance meets requirements with large datasets

### User Story 8: Documentation & Examples
**As a** developer  
**I want to** comprehensive documentation and examples  
**So that** I can easily use the column management system

#### Tasks:
- [ ] Create developer documentation for the column management system
- [ ] Add code examples for common use cases
- [ ] Document user preference system
- [ ] Update existing documentation to reflect changes
- [ ] Create a migration guide for existing code

**Acceptance Criteria:**
- Documentation covers all aspects of the column management system
- Examples demonstrate common use cases
- Migration guide helps developers update existing code

## Definition of Done (for all user stories):

1. Code follows project coding standards
2. Unit tests cover functionality
3. Integration tests verify system behavior
4. Documentation is updated
5. Code is reviewed by at least one other developer
6. All acceptance criteria are met
7. No regression in existing functionality
