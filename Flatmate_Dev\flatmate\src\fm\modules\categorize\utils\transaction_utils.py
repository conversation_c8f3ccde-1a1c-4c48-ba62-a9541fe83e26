"""
Transaction utilities for the categorize module.

Simple utilities for converting transactions to DataFrames with proper column names.
Uses the new data_services system for elegant column handling.
"""

import pandas as pd
from typing import List
from fm.core.data_services import convert_transactions_to_dataframe


def transactions_to_dataframe(transactions: List) -> pd.DataFrame:
    """
    Convert Transaction objects to DataFrame with proper db_name columns.

    This is now a simple wrapper around the data_services convert_transactions_to_dataframe
    function which provides the elegant solution for transaction conversion.

    Args:
        transactions: List of Transaction objects from database

    Returns:
        DataFrame with db_name columns ready for TableView display
    """
    # Use the enhanced data_services method
    return convert_transactions_to_dataframe(
        transactions,
        ensure_columns=['category', 'tags', 'notes']  # Categorize module requirements
    )
