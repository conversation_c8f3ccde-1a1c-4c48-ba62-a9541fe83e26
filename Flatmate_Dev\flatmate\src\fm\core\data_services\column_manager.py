"""
Column Manager Service

This module provides a centralized service for managing column operations,
including filtering columns by usage, handling user preferences, and
conditional display of columns based on data presence.
"""

import pandas as pd
from typing import List, Dict, Optional, Set, Any
from .utils.standard_columns import StandardColumns, ColumnUsage
from fm.core.standards.fm_standard_columns import StandardColumns as CanonicalColumns


class ColumnManager:
    """
    Central service for managing column operations across the application.
    
    This class provides methods for:
    - Getting columns by usage category
    - Converting between display and database column names
    - Filtering display columns based on data presence
    - Applying user display preferences
    """
    
    def __init__(self, user_preferences: Optional[Dict[str, Any]] = None):
        """
        Initialize the column manager.
        
        Args:
            user_preferences: Optional dictionary of user preferences for column display
        """
        self.user_preferences = user_preferences or {}
    
    def get_columns_by_usage(self, usage: str) -> List[StandardColumns]:
        """
        Get all columns for a specific usage category.
        
        Args:
            usage: Usage category from ColumnUsage
            
        Returns:
            List of columns that belong to the specified usage category
        """
        return StandardColumns.get_columns_by_usage(usage)
    
    def get_source_columns(self) -> List[StandardColumns]:
        """Get columns used in source data (bank statements)"""
        return self.get_columns_by_usage(ColumnUsage.SOURCE.value)
    
    def get_display_columns(self) -> List[StandardColumns]:
        """Get columns used for display in the UI"""
        return self.get_columns_by_usage(ColumnUsage.DISPLAY.value)
    
    def get_categorize_columns(self) -> List[StandardColumns]:
        """Get columns used for categorization"""
        return self.get_columns_by_usage(ColumnUsage.CATEGORIZE.value)
    
    def get_system_columns(self) -> List[StandardColumns]:
        """Get columns used for internal system purposes"""
        return self.get_columns_by_usage(ColumnUsage.SYSTEM.value)
    
    def get_display_columns_with_data(self, df: pd.DataFrame) -> List[StandardColumns]:
        """
        Get display columns that actually contain data in the given DataFrame.
        
        This filters the display columns to only include those that have
        non-null values in at least one row of the DataFrame.
        
        Args:
            df: DataFrame containing transaction data
            
        Returns:
            List of display columns that have data
        """
        display_columns = self.get_display_columns()
        columns_with_data = []
        
        for col in display_columns:
            db_name = col.db_name
            # Check if column exists in DataFrame and has non-null values
            if db_name in df.columns and not df[db_name].isna().all():
                columns_with_data.append(col)
        
        return columns_with_data
    
    def convert_to_db_columns(self, display_columns: List[str]) -> List[str]:
        """
        Convert display column names to database column names.
        
        Args:
            display_columns: List of display column names
            
        Returns:
            List of corresponding database column names
        """
        result = []
        for display_name in display_columns:
            col = StandardColumns.from_display_name(display_name)
            if col:
                result.append(col.db_name)
        return result
    
    def convert_to_display_columns(self, db_columns: List[str]) -> List[str]:
        """
        Convert database column names to display column names.
        
        Args:
            db_columns: List of database column names
            
        Returns:
            List of corresponding display column names
        """
        result = []
        for db_name in db_columns:
            col = StandardColumns.from_db_name(db_name)
            if col:
                result.append(col.value.display_name)
        return result
    
    def apply_user_preferences(self, columns: List[StandardColumns]) -> List[Dict[str, Any]]:
        """
        Apply user preferences to column definitions.
        
        This method applies user preferences such as custom display names,
        column order, and visibility to the provided columns.
        
        Args:
            columns: List of StandardColumns to apply preferences to
            
        Returns:
            List of column definitions with user preferences applied
        """
        result = []
        
        # Get user preferences for column display
        custom_names = self.user_preferences.get('custom_names', {})
        column_order = self.user_preferences.get('column_order', [])
        hidden_columns = self.user_preferences.get('hidden_columns', [])
        
        # Create column definitions with user preferences applied
        for col in columns:
            # Skip hidden columns
            if col.name in hidden_columns:
                continue
                
            # Create column definition
            col_def = {
                'db_name': col.db_name,
                'display_name': custom_names.get(col.name, col.value.display_name),
                'editable': col.value.editable,
                'width': col.value.width,
                'description': col.value.description
            }
            result.append(col_def)
        
        # Sort columns according to user preference if specified
        if column_order:
            # Create a mapping of column names to their position in the order
            order_map = {name: idx for idx, name in enumerate(column_order)}
            
            # Sort the result based on the order map
            result.sort(key=lambda x: order_map.get(x['db_name'], float('inf')))
        
        return result
    
    def get_editable_columns(self) -> List[StandardColumns]:
        """
        Get columns that are editable by the user.
        
        Returns:
            List of columns that are editable
        """
        return [col for col in StandardColumns if col.value.editable]
    
    def get_column_widths(self) -> Dict[str, int]:
        """
        Get column widths for UI display.
        
        Returns:
            Dictionary mapping display names to widths
        """
        # Start with standard widths
        widths = StandardColumns.get_standard_column_widths()
        
        # Apply user preferences if any
        custom_widths = self.user_preferences.get('column_widths', {})
        widths.update(custom_widths)
        
        return widths
    
    def update_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """
        Update user preferences.
        
        Args:
            preferences: Dictionary of user preferences to update
        """
        self.user_preferences.update(preferences)
    
    def get_df_with_display_columns(self, df: pd.DataFrame, only_columns_with_data: bool = True) -> pd.DataFrame:
        """
        Get a DataFrame with display column names.
        
        This converts the database column names in the DataFrame to display names
        and optionally filters to only include columns with data.
        
        Args:
            df: DataFrame with database column names
            only_columns_with_data: If True, only include columns with data
            
        Returns:
            DataFrame with display column names
        """
        # Make a copy to avoid modifying the original
        result_df = df.copy()
        
        # Get columns to include
        if only_columns_with_data:
            columns = self.get_display_columns_with_data(df)
        else:
            columns = self.get_display_columns()
        
        # Filter to only include display columns that exist in the DataFrame
        db_names = [col.db_name for col in columns if col.db_name in df.columns]
        
        # Create a mapping of db names to display names
        name_mapping = {col.db_name: col.value.display_name for col in columns}
        
        # Filter and rename columns
        result_df = result_df[db_names].rename(columns=name_mapping)
        
        return result_df

    # === Migrated functions from column_name_service.py ===

    def get_display_mapping(self, df_columns: List[str],
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get complete db_name -> display_name mapping for DataFrame columns.

        Args:
            df_columns: List of column names from DataFrame (db_names)
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            Complete mapping: db_name -> display_name
        """
        mapping = {}

        # Create reverse mapping from CanonicalColumns (db_name -> display_name)
        standard_reverse = {col.db_name: col.value for col in CanonicalColumns}

        for db_col in df_columns:
            if custom_mapping and db_col in custom_mapping:
                # Custom override takes precedence
                mapping[db_col] = custom_mapping[db_col]
            elif db_col in standard_reverse:
                # Use CanonicalColumns canonical display name
                mapping[db_col] = standard_reverse[db_col]
            else:
                # Fallback for unknown columns - convert snake_case to Title Case
                mapping[db_col] = db_col.replace('_', ' ').title()

        return mapping

    def get_reverse_mapping(self, df_columns: List[str],
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get complete display_name -> db_name mapping for DataFrame columns.

        Args:
            df_columns: List of column names from DataFrame (db_names)
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            Complete reverse mapping: display_name -> db_name
        """
        display_mapping = self.get_display_mapping(df_columns, custom_mapping)
        return {display_name: db_name for db_name, display_name in display_mapping.items()}

    def apply_display_names(self, df: pd.DataFrame,
                          custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Apply display names to DataFrame columns.

        NOTE: Date formatting is now handled by the table model to preserve sorting.

        Args:
            df: DataFrame with db_names as columns
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            New DataFrame with display names as columns (dates kept as original objects)
        """
        if df.empty:
            return df.copy()

        display_mapping = self.get_display_mapping(
            df.columns.tolist(),
            custom_mapping
        )

        display_df = df.copy()

        # Apply display names to columns (keep original date objects for proper sorting)
        display_df.columns = [display_mapping.get(col, col) for col in df.columns]
        return display_df

    def apply_db_names(self, df: pd.DataFrame,
                      custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Convert DataFrame with display names back to db_names.

        Args:
            df: DataFrame with display_names as columns
            custom_mapping: Optional custom db_name -> display_name overrides

        Returns:
            New DataFrame with db_names as columns
        """
        if df.empty:
            return df.copy()

        # Get reverse mapping (display_name -> db_name)
        reverse_mapping = self.get_reverse_mapping(
            # We need to reverse-engineer the db_names from display_names
            # This is a bit tricky, so we'll use CanonicalColumns as reference
            [col.db_name for col in CanonicalColumns] +
            [col.lower().replace(' ', '_') for col in df.columns if col not in [sc.value for sc in CanonicalColumns]],
            custom_mapping
        )

        db_df = df.copy()
        db_df.columns = [reverse_mapping.get(col, col.lower().replace(' ', '_')) for col in df.columns]
        return db_df

    def get_default_visible_columns_for_categorize(self) -> List[str]:
        """
        Get the default visible columns for the categorize module.

        This is the centralized source of truth for which columns should be
        visible by default in the categorize module.

        Returns:
            List of db_names that should be visible by default
        """
        return [
            CanonicalColumns.DATE.db_name,        # 'date'
            CanonicalColumns.DETAILS.db_name,     # 'details'
            CanonicalColumns.AMOUNT.db_name,      # 'amount'
            CanonicalColumns.ACCOUNT.db_name,     # 'account'
            CanonicalColumns.TAGS.db_name,        # 'tags'
            CanonicalColumns.CATEGORY.db_name,    # 'category'
        ]

    def get_default_visible_columns_for_module(self, module_name: str) -> List[str]:
        """
        Get default visible columns for any module.

        Args:
            module_name: Name of the module ('categorize', 'reports', etc.)

        Returns:
            List of db_names that should be visible by default
        """
        if module_name == 'categorize':
            return self.get_default_visible_columns_for_categorize()
        elif module_name == 'reports':
            # Future: Add report module defaults
            return self.get_default_visible_columns_for_categorize()  # Fallback
        else:
            # Default fallback - core transaction columns
            return [
                CanonicalColumns.DATE.db_name,
                CanonicalColumns.DETAILS.db_name,
                CanonicalColumns.AMOUNT.db_name,
                CanonicalColumns.ACCOUNT.db_name,
            ]

    def convert_transactions_to_dataframe(self, transactions: List,
                                        ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Convert Transaction objects to DataFrame with proper db_name columns.

        This replaces the complex transaction_utils.transactions_to_dataframe function
        with a cleaner, more maintainable approach.

        Args:
            transactions: List of Transaction objects from database
            ensure_columns: Optional list of db_name columns to ensure exist (with empty values)

        Returns:
            DataFrame with db_name columns ready for display conversion
        """
        if not transactions:
            return pd.DataFrame()

        # Convert transactions to list of dictionaries using CanonicalColumns mapping
        data = []
        for transaction in transactions:
            trans_dict = {}

            # Map all CanonicalColumns fields
            for std_col in CanonicalColumns:
                db_name = std_col.db_name

                # Handle special field name mappings
                if db_name == 'details' and hasattr(transaction, 'description'):
                    trans_dict[db_name] = getattr(transaction, 'description', '')
                elif db_name == 'account' and hasattr(transaction, 'account_number'):
                    trans_dict[db_name] = getattr(transaction, 'account_number', '')
                elif db_name == 'id' and hasattr(transaction, 'transaction_id'):
                    trans_dict[db_name] = getattr(transaction, 'transaction_id', '')
                elif hasattr(transaction, db_name):
                    trans_dict[db_name] = getattr(transaction, db_name, '')
                else:
                    # Set empty value for missing fields
                    trans_dict[db_name] = ''

            data.append(trans_dict)

        # Create DataFrame
        df = pd.DataFrame(data)

        # Ensure additional columns exist if requested
        if ensure_columns:
            for col in ensure_columns:
                if col not in df.columns:
                    df[col] = ""

        return df


# === Convenience functions for backward compatibility ===

def get_column_display_mapping(df_columns: List[str],
                             custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
    """Convenience function for getting display mapping."""
    manager = ColumnManager()
    return manager.get_display_mapping(df_columns, custom_mapping)


def apply_display_names_to_dataframe(df: pd.DataFrame,
                                   custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
    """Convenience function for applying display names."""
    manager = ColumnManager()
    return manager.apply_display_names(df, custom_mapping)


def get_default_visible_columns_for_module(module_name: str) -> List[str]:
    """Convenience function for getting module default columns."""
    manager = ColumnManager()
    return manager.get_default_visible_columns_for_module(module_name)


def convert_transactions_to_dataframe(transactions: List,
                                    ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
    """Convenience function for converting transactions to DataFrame."""
    manager = ColumnManager()
    return manager.convert_transactions_to_dataframe(transactions, ensure_columns)
