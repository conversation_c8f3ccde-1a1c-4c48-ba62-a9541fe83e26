"""
Column Manager Service

This module provides a centralized service for managing column operations,
including filtering columns by usage, handling user preferences, and
conditional display of columns based on data presence.
"""

import pandas as pd
from typing import List, Dict, Optional, Set, Any
from .standard_columns import StandardColumns, ColumnUsage


class ColumnManager:
    """
    Central service for managing column operations across the application.
    
    This class provides methods for:
    - Getting columns by usage category
    - Converting between display and database column names
    - Filtering display columns based on data presence
    - Applying user display preferences
    """
    
    def __init__(self, user_preferences: Optional[Dict[str, Any]] = None):
        """
        Initialize the column manager.
        
        Args:
            user_preferences: Optional dictionary of user preferences for column display
        """
        self.user_preferences = user_preferences or {}
    
    def get_columns_by_usage(self, usage: str) -> List[StandardColumns]:
        """
        Get all columns for a specific usage category.
        
        Args:
            usage: Usage category from ColumnUsage
            
        Returns:
            List of columns that belong to the specified usage category
        """
        return StandardColumns.get_columns_by_usage(usage)
    
    def get_source_columns(self) -> List[StandardColumns]:
        """Get columns used in source data (bank statements)"""
        return self.get_columns_by_usage(ColumnUsage.SOURCE.value)
    
    def get_display_columns(self) -> List[StandardColumns]:
        """Get columns used for display in the UI"""
        return self.get_columns_by_usage(ColumnUsage.DISPLAY.value)
    
    def get_categorize_columns(self) -> List[StandardColumns]:
        """Get columns used for categorization"""
        return self.get_columns_by_usage(ColumnUsage.CATEGORIZE.value)
    
    def get_system_columns(self) -> List[StandardColumns]:
        """Get columns used for internal system purposes"""
        return self.get_columns_by_usage(ColumnUsage.SYSTEM.value)
    
    def get_display_columns_with_data(self, df: pd.DataFrame) -> List[StandardColumns]:
        """
        Get display columns that actually contain data in the given DataFrame.
        
        This filters the display columns to only include those that have
        non-null values in at least one row of the DataFrame.
        
        Args:
            df: DataFrame containing transaction data
            
        Returns:
            List of display columns that have data
        """
        display_columns = self.get_display_columns()
        columns_with_data = []
        
        for col in display_columns:
            db_name = col.db_name
            # Check if column exists in DataFrame and has non-null values
            if db_name in df.columns and not df[db_name].isna().all():
                columns_with_data.append(col)
        
        return columns_with_data
    
    def convert_to_db_columns(self, display_columns: List[str]) -> List[str]:
        """
        Convert display column names to database column names.
        
        Args:
            display_columns: List of display column names
            
        Returns:
            List of corresponding database column names
        """
        result = []
        for display_name in display_columns:
            col = StandardColumns.from_display_name(display_name)
            if col:
                result.append(col.db_name)
        return result
    
    def convert_to_display_columns(self, db_columns: List[str]) -> List[str]:
        """
        Convert database column names to display column names.
        
        Args:
            db_columns: List of database column names
            
        Returns:
            List of corresponding display column names
        """
        result = []
        for db_name in db_columns:
            col = StandardColumns.from_db_name(db_name)
            if col:
                result.append(col.value.display_name)
        return result
    
    def apply_user_preferences(self, columns: List[StandardColumns]) -> List[Dict[str, Any]]:
        """
        Apply user preferences to column definitions.
        
        This method applies user preferences such as custom display names,
        column order, and visibility to the provided columns.
        
        Args:
            columns: List of StandardColumns to apply preferences to
            
        Returns:
            List of column definitions with user preferences applied
        """
        result = []
        
        # Get user preferences for column display
        custom_names = self.user_preferences.get('custom_names', {})
        column_order = self.user_preferences.get('column_order', [])
        hidden_columns = self.user_preferences.get('hidden_columns', [])
        
        # Create column definitions with user preferences applied
        for col in columns:
            # Skip hidden columns
            if col.name in hidden_columns:
                continue
                
            # Create column definition
            col_def = {
                'db_name': col.db_name,
                'display_name': custom_names.get(col.name, col.value.display_name),
                'editable': col.value.editable,
                'width': col.value.width,
                'description': col.value.description
            }
            result.append(col_def)
        
        # Sort columns according to user preference if specified
        if column_order:
            # Create a mapping of column names to their position in the order
            order_map = {name: idx for idx, name in enumerate(column_order)}
            
            # Sort the result based on the order map
            result.sort(key=lambda x: order_map.get(x['db_name'], float('inf')))
        
        return result
    
    def get_editable_columns(self) -> List[StandardColumns]:
        """
        Get columns that are editable by the user.
        
        Returns:
            List of columns that are editable
        """
        return [col for col in StandardColumns if col.value.editable]
    
    def get_column_widths(self) -> Dict[str, int]:
        """
        Get column widths for UI display.
        
        Returns:
            Dictionary mapping display names to widths
        """
        # Start with standard widths
        widths = StandardColumns.get_standard_column_widths()
        
        # Apply user preferences if any
        custom_widths = self.user_preferences.get('column_widths', {})
        widths.update(custom_widths)
        
        return widths
    
    def update_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """
        Update user preferences.
        
        Args:
            preferences: Dictionary of user preferences to update
        """
        self.user_preferences.update(preferences)
    
    def get_df_with_display_columns(self, df: pd.DataFrame, only_columns_with_data: bool = True) -> pd.DataFrame:
        """
        Get a DataFrame with display column names.
        
        This converts the database column names in the DataFrame to display names
        and optionally filters to only include columns with data.
        
        Args:
            df: DataFrame with database column names
            only_columns_with_data: If True, only include columns with data
            
        Returns:
            DataFrame with display column names
        """
        # Make a copy to avoid modifying the original
        result_df = df.copy()
        
        # Get columns to include
        if only_columns_with_data:
            columns = self.get_display_columns_with_data(df)
        else:
            columns = self.get_display_columns()
        
        # Filter to only include display columns that exist in the DataFrame
        db_names = [col.db_name for col in columns if col.db_name in df.columns]
        
        # Create a mapping of db names to display names
        name_mapping = {col.db_name: col.value.display_name for col in columns}
        
        # Filter and rename columns
        result_df = result_df[db_names].rename(columns=name_mapping)
        
        return result_df
