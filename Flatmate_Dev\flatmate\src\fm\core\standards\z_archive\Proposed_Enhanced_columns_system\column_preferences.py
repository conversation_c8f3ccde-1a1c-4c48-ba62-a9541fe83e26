"""
Column preferences system for managing user customizations.

This module handles user preferences for column display names, visibility,
ordering, and other customizations across different modules.
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field

from .enhanced_columns import EnhancedStandardColumns


@dataclass
class ModuleColumnPreferences:
    """Column preferences for a specific module."""
    module_name: str
    visible_columns: List[str] = field(default_factory=list)  # db_names of visible columns
    column_order: List[str] = field(default_factory=list)     # db_names in display order
    column_widths: Dict[str, int] = field(default_factory=dict)  # display_name -> width
    custom_display_names: Dict[str, str] = field(default_factory=dict)  # db_name -> custom_name
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'module_name': self.module_name,
            'visible_columns': self.visible_columns,
            'column_order': self.column_order,
            'column_widths': self.column_widths,
            'custom_display_names': self.custom_display_names
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModuleColumnPreferences':
        """Create from dictionary."""
        return cls(
            module_name=data['module_name'],
            visible_columns=data.get('visible_columns', []),
            column_order=data.get('column_order', []),
            column_widths=data.get('column_widths', {}),
            custom_display_names=data.get('custom_display_names', {})
        )


class ColumnPreferences:
    """
    Manages user preferences for column display across all modules.
    
    This class handles:
    - Custom display names for columns
    - Column visibility per module
    - Column ordering per module
    - Column width preferences
    - Persistence to configuration files
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        Initialize column preferences.
        
        Args:
            config_dir: Directory to store preference files. If None, uses default.
        """
        self.config_dir = config_dir or Path.home() / '.flatmate' / 'config'
        self.config_file = self.config_dir / 'column_preferences.json'
        self.module_preferences: Dict[str, ModuleColumnPreferences] = {}
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Load existing preferences
        self._load_preferences()
    
    def _load_preferences(self):
        """Load preferences from configuration file."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                for module_name, module_data in data.items():
                    self.module_preferences[module_name] = ModuleColumnPreferences.from_dict(module_data)
            
            except (json.JSONDecodeError, KeyError, FileNotFoundError) as e:
                print(f"Warning: Could not load column preferences: {e}")
                # Continue with empty preferences
    
    def _save_preferences(self):
        """Save preferences to configuration file."""
        try:
            data = {
                module_name: prefs.to_dict() 
                for module_name, prefs in self.module_preferences.items()
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
        
        except Exception as e:
            print(f"Warning: Could not save column preferences: {e}")
    
    def get_module_preferences(self, module_name: str) -> ModuleColumnPreferences:
        """Get preferences for a specific module, creating defaults if needed."""
        if module_name not in self.module_preferences:
            self.module_preferences[module_name] = self._create_default_preferences(module_name)
        
        return self.module_preferences[module_name]
    
    def _create_default_preferences(self, module_name: str) -> ModuleColumnPreferences:
        """Create default preferences for a module."""
        # Get default columns based on module type
        default_columns = self._get_default_columns_for_module(module_name)
        
        return ModuleColumnPreferences(
            module_name=module_name,
            visible_columns=[col.db_name for col in default_columns],
            column_order=[col.db_name for col in default_columns],
            column_widths={col.display_name: col.definition.default_width for col in default_columns},
            custom_display_names={}
        )
    
    def _get_default_columns_for_module(self, module_name: str) -> List[EnhancedStandardColumns]:
        """Get default columns for a specific module."""
        # Try to get defaults from module's config system first
        config_defaults = self._get_config_defaults_for_module(module_name)
        if config_defaults:
            return config_defaults

        # Fall back to hardcoded defaults
        if module_name == "categorize":
            return [
                EnhancedStandardColumns.DATE,
                EnhancedStandardColumns.DETAILS,
                EnhancedStandardColumns.AMOUNT,
                EnhancedStandardColumns.ACCOUNT,
                EnhancedStandardColumns.TAGS,
                EnhancedStandardColumns.CATEGORY
            ]
        elif module_name == "update_data":
            return [
                EnhancedStandardColumns.DATE,
                EnhancedStandardColumns.DETAILS,
                EnhancedStandardColumns.AMOUNT,
                EnhancedStandardColumns.ACCOUNT,
                EnhancedStandardColumns.SOURCE_FILENAME
            ]
        else:
            # Default to required columns plus common ones
            required = EnhancedStandardColumns.get_required_columns()
            common = [EnhancedStandardColumns.TAGS, EnhancedStandardColumns.CATEGORY]
            return required + common

    def _get_config_defaults_for_module(self, module_name: str) -> Optional[List[EnhancedStandardColumns]]:
        """Try to get default columns from module's config system."""
        try:
            if module_name == "categorize":
                # Import categorize config and get default columns
                from fm.modules.categorize.config import config
                default_column_names = config.get_value('categorize.display.default_columns', None)
                if default_column_names:
                    # Convert column names to EnhancedStandardColumns
                    columns = []
                    for col_name in default_column_names:
                        column = EnhancedStandardColumns.find_by_db_name(col_name)
                        if column:
                            columns.append(column)
                    return columns if columns else None
            # Add other modules here as they migrate to config V2
            return None
        except (ImportError, AttributeError, Exception):
            # If config system is not available or fails, return None to use hardcoded defaults
            return None

    # Public API methods
    def set_custom_display_name(self, module_name: str, db_name: str, display_name: str):
        """Set a custom display name for a column in a module."""
        prefs = self.get_module_preferences(module_name)
        prefs.custom_display_names[db_name] = display_name
        self._save_preferences()
    
    def get_custom_display_name(self, module_name: str, db_name: str) -> Optional[str]:
        """Get custom display name for a column, if set."""
        prefs = self.get_module_preferences(module_name)
        return prefs.custom_display_names.get(db_name)
    
    def set_visible_columns(self, module_name: str, db_names: List[str]):
        """Set which columns are visible in a module."""
        prefs = self.get_module_preferences(module_name)
        
        # Ensure required columns are always included
        required_db_names = [col.db_name for col in EnhancedStandardColumns.get_required_columns()]
        visible_set = set(db_names)
        visible_set.update(required_db_names)
        
        prefs.visible_columns = list(visible_set)
        self._save_preferences()
    
    def get_visible_columns(self, module_name: str) -> List[str]:
        """Get list of visible column db_names for a module."""
        prefs = self.get_module_preferences(module_name)
        return prefs.visible_columns.copy()
    
    def set_column_order(self, module_name: str, db_names: List[str]):
        """Set the display order of columns in a module."""
        prefs = self.get_module_preferences(module_name)
        prefs.column_order = db_names.copy()
        self._save_preferences()
    
    def get_column_order(self, module_name: str) -> List[str]:
        """Get the display order of columns for a module."""
        prefs = self.get_module_preferences(module_name)
        return prefs.column_order.copy()
    
    def set_column_width(self, module_name: str, display_name: str, width: int):
        """Set the width of a column in a module."""
        prefs = self.get_module_preferences(module_name)
        prefs.column_widths[display_name] = width
        self._save_preferences()
    
    def get_column_widths(self, module_name: str) -> Dict[str, int]:
        """Get column widths for a module."""
        prefs = self.get_module_preferences(module_name)
        return prefs.column_widths.copy()
    
    def reset_module_preferences(self, module_name: str):
        """Reset preferences for a module to defaults."""
        if module_name in self.module_preferences:
            del self.module_preferences[module_name]
        
        # This will create new defaults when accessed
        self._save_preferences()
    
    def export_preferences(self, file_path: Path):
        """Export preferences to a file."""
        data = {
            module_name: prefs.to_dict() 
            for module_name, prefs in self.module_preferences.items()
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def import_preferences(self, file_path: Path):
        """Import preferences from a file."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        for module_name, module_data in data.items():
            self.module_preferences[module_name] = ModuleColumnPreferences.from_dict(module_data)
        
        self._save_preferences()


# Global instance for easy access
_global_preferences: Optional[ColumnPreferences] = None

def get_column_preferences() -> ColumnPreferences:
    """Get the global column preferences instance."""
    global _global_preferences
    if _global_preferences is None:
        _global_preferences = ColumnPreferences()
    return _global_preferences
