"""
Database I/O module for transaction storage and retrieval.
Provides a clean interface for working with transaction data.
"""
from .sql_repository.transaction_repository import ImportResult, Transaction, TransactionRepository
from .sql_repository.sqlite_repository import SQLiteTransactionRepository
from .io_service_utils.events import event_bus, DataEvents
from .io_service_utils.converters import CSVToTransactionConverter, TransactionToCSVConverter
from .db_io_service import DBIOService

# Create singleton instance for application-wide use
db_io_service = DBIOService()

__all__ = [
    'Transaction',
    'ImportResult',
    'TransactionRepository',
    'SQLiteTransactionRepository',
    'DataEvents',
    'CSVToTransactionConverter',
    'TransactionToCSVConverter',
    'DBIOService',
    'db_io_service',
    'event_bus'
]
