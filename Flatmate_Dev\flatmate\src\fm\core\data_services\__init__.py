"""
Core Data Services Package

This package provides centralized data services for the application,
including column management, database I/O, and data transformation.
"""

from .utils.standard_columns import StandardColumns, ColumnUsage, ColumnMetadata
from .utils.converters import CSVToTransactionConverter, TransactionToCSVConverter
from .utils.events import event_bus, DataEvents
from .column_manager import (
    ColumnManager,
    get_column_display_mapping,
    apply_display_names_to_dataframe,
    get_default_visible_columns_for_module,
    convert_transactions_to_dataframe
)
from .db_io_service import DBIOService
from .date_format_service import DateFormatService, DateDisplayFormat

__all__ = [
    'StandardColumns',
    'ColumnUsage',
    'ColumnMetadata',
    'ColumnManager',
    'DBIOService',
    'DateFormatService',
    'DateDisplayFormat',
    'CSVToTransactionConverter',
    'TransactionToCSVConverter',
    'event_bus',
    'DataEvents',
    # Convenience functions for backward compatibility
    'get_column_display_mapping',
    'apply_display_names_to_dataframe',
    'get_default_visible_columns_for_module',
    'convert_transactions_to_dataframe',
]
