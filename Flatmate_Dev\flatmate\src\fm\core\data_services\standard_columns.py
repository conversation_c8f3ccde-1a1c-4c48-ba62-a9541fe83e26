"""
Enhanced StandardColumns definition with rich metadata.

This module provides a centralized definition of all column names used in the application,
with rich metadata about each column's usage, display properties, and behavior.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any


class ColumnUsage(Enum):
    """Usage categories for columns"""
    SOURCE = "source"      # Found in raw bank statements
    DISPLAY = "display"    # Shown in the UI
    CATEGORIZE = "categorize"  # Used for categorization
    SYSTEM = "system"      # Internal use only


@dataclass
class ColumnMetadata:
    """Metadata for a standard column"""
    display_name: str
    used_in: List[str]  # Values from ColumnUsage
    editable: bool = False
    width: Optional[int] = None
    description: Optional[str] = None
    default_value: Any = None


class StandardColumns(Enum):
    """
    Standardized column names for bank transactions with rich metadata.
    
    This enum serves as the single source of truth for all column definitions
    in the application. Each column has associated metadata describing its
    usage, display properties, and behavior.
    """
    # Core Transaction Identifiers
    DATE = ColumnMetadata(
        display_name="Date",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=False,
        width=12,
        description="Transaction date"
    )
    
    DETAILS = ColumnMetadata(
        display_name="Details",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=False,
        width=40,
        description="Transaction details/description"
    )
    
    AMOUNT = ColumnMetadata(
        display_name="Amount",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=False,
        width=12,
        description="Transaction amount"
    )
    
    BALANCE = ColumnMetadata(
        display_name="Balance",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=12,
        description="Account balance after transaction"
    )
    
    ACCOUNT = ColumnMetadata(
        display_name="Account",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=False,
        width=15,
        description="Account number or identifier"
    )
    
    # Unique Transaction Identifier (if present in source)
    UNIQUE_ID = ColumnMetadata(
        display_name="Unique Id",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.SYSTEM.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="Unique transaction identifier used by some banks"
    )
    
    # Amount Columns
    CREDIT_AMOUNT = ColumnMetadata(
        display_name="Credit",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=12,
        description="Credit amount (positive transactions)"
    )
    
    DEBIT_AMOUNT = ColumnMetadata(
        display_name="Debit",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=12,
        description="Debit amount (negative transactions)"
    )
    
    # Source Info
    SOURCE_FILENAME = ColumnMetadata(
        display_name="Source Filename",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.SYSTEM.value],
        editable=False,
        width=20,
        description="Original filename of imported data"
    )
    
    # This Party (TP) Details - Account Holder's Side
    PAYMENT_TYPE = ColumnMetadata(
        display_name="Payment Type",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="Type of payment (e.g., EFTPOS, Direct Debit)"
    )
    
    TP_REF = ColumnMetadata(
        display_name="TP Ref",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="This party reference"
    )
    
    TP_PART = ColumnMetadata(
        display_name="TP Part",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="This party particulars"
    )
    
    TP_CODE = ColumnMetadata(
        display_name="TP Code",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="This party code"
    )
    
    # Other Party (OP) Details - Counterparty Side
    OP_REF = ColumnMetadata(
        display_name="OP Ref",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="Other party reference"
    )
    
    OP_PART = ColumnMetadata(
        display_name="OP Part",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="Other party particulars"
    )
    
    OP_CODE = ColumnMetadata(
        display_name="OP Code",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="Other party code"
    )
    
    OP_NAME = ColumnMetadata(
        display_name="OP Name",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=False,
        width=20,
        description="Other party name"
    )
    
    OP_ACCOUNT = ColumnMetadata(
        display_name="OP Account",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.DISPLAY.value],
        editable=False,
        width=15,
        description="Other party account number"
    )
    
    # Categorization and User Fields
    CATEGORY = ColumnMetadata(
        display_name="Category",
        used_in=[ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=True,
        width=20,
        description="Transaction category"
    )
    
    TAGS = ColumnMetadata(
        display_name="Tags",
        used_in=[ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=True,
        width=20,
        description="Transaction tags (comma-separated)"
    )
    
    NOTES = ColumnMetadata(
        display_name="Notes",
        used_in=[ColumnUsage.DISPLAY.value, ColumnUsage.CATEGORIZE.value],
        editable=True,
        width=30,
        description="User notes about the transaction"
    )
    
    # System Fields
    ID = ColumnMetadata(
        display_name="ID",
        used_in=[ColumnUsage.SYSTEM.value],
        editable=False,
        description="Internal database ID"
    )
    
    SOURCE_BANK = ColumnMetadata(
        display_name="Source Bank",
        used_in=[ColumnUsage.SOURCE.value, ColumnUsage.SYSTEM.value],
        editable=False,
        width=15,
        description="Source bank identifier"
    )
    
    IMPORT_DATE = ColumnMetadata(
        display_name="Import Date",
        used_in=[ColumnUsage.SYSTEM.value],
        editable=False,
        width=12,
        description="Date when transaction was imported"
    )
    
    MODIFIED_DATE = ColumnMetadata(
        display_name="Modified Date",
        used_in=[ColumnUsage.SYSTEM.value],
        editable=False,
        width=12,
        description="Date when transaction was last modified"
    )
    
    EMPTY_COLUMN = ColumnMetadata(
        display_name="Empty",
        used_in=[ColumnUsage.SOURCE.value],
        editable=False,
        description="Placeholder for empty columns in source data"
    )
    
    @property
    def db_name(self):
        """
        Get the database-friendly column name
        
        Simply converts the enum name to lowercase for consistency.
        """
        return self.name.lower()
    
    @property
    def metadata(self) -> ColumnMetadata:
        """Get column metadata"""
        return self.value
    
    @classmethod
    def get_columns_by_usage(cls, usage: str) -> List['StandardColumns']:
        """
        Get all columns for a specific usage category
        
        Args:
            usage: Usage category from ColumnUsage
            
        Returns:
            List of columns that belong to the specified usage category
        """
        return [col for col in cls if usage in col.value.used_in]
    
    @classmethod
    def from_display_name(cls, display_name: str) -> Optional['StandardColumns']:
        """
        Get the enum member from a display name
        
        Args:
            display_name: The display name to look up
            
        Returns:
            The matching enum member or None if not found
        """
        for col in cls:
            if col.value.display_name == display_name:
                return col
        return None
    
    @classmethod
    def from_db_name(cls, db_name: str) -> Optional['StandardColumns']:
        """
        Get the enum member from a database column name
        
        Args:
            db_name: The database column name to look up
            
        Returns:
            The matching enum member or None if not found
        """
        try:
            return cls[db_name.upper()]
        except KeyError:
            return None
    
    @classmethod
    def get_db_column_mapping(cls) -> Dict[str, str]:
        """
        Get a dictionary mapping display names to database column names
        
        Returns:
            Dictionary mapping display names to database column names
        """
        return {col.value.display_name: col.db_name for col in cls}
    
    @classmethod
    def get_display_column_mapping(cls) -> Dict[str, str]:
        """
        Get a dictionary mapping database column names to display names
        
        Returns:
            Dictionary mapping database column names to display names
        """
        return {col.db_name: col.value.display_name for col in cls}
    
    @classmethod
    def get_standard_column_widths(cls) -> Dict[str, int]:
        """
        Get standard column widths for consistent UI display.
        
        Returns:
            Dictionary mapping display names to character widths
        """
        return {col.value.display_name: col.value.width 
                for col in cls if col.value.width is not None}
    
    @classmethod
    def get_core_columns_in_order(cls) -> List[tuple]:
        """
        Get the core transaction columns in standard display order.
        
        Returns:
            List of (db_name, display_name) tuples in standard order
        """
        core_order = [
            cls.DATE,
            cls.DETAILS,
            cls.AMOUNT,
            cls.ACCOUNT,
        ]
        return [(col.db_name, col.value.display_name) for col in core_order]


# For backward compatibility
FmColumnFormat = StandardColumns
