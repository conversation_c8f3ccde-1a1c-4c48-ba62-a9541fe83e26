"""Database command-line interface commands.

Refactored version using the db_io_service layer.
"""
import argparse
import csv
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from PySide6.QtWidgets import QApplication, QFileDialog, QWidget

import pandas as pd
from fm.core.database_v2 import Transaction, db_io_service
from fm.core.standards.fm_standard_columns import StandardColumns

def get_transactions(args) -> List[Transaction]:
    """Retrieve transactions based on command line arguments."""
    filters = {}
    if hasattr(args, 'start_date') and args.start_date:
        filters["start_date"] = args.start_date
    if hasattr(args, 'end_date') and args.end_date:
        filters["end_date"] = args.end_date
    if hasattr(args, 'limit') and args.limit:
        filters["limit"] = args.limit
    
    return db_io_service.list_transactions(**filters)

def list_transactions(args) -> None:
    """List transactions to the console."""
    transactions = get_transactions(args)
    if not transactions:
        print("No transactions found.")
        return
    
    # Print header
    headers = [
        "Date",
        "Description",
        "Amount",
        "Account",
        "Type",
        "Category"
    ]
    print("\n" + " | ".join(f"{h:<15}" for h in headers))
    print("-" * 100)
    
    # Print transactions
    for t in transactions:
        print(
            f"{t.date.strftime('%d-%m-%Y') if t.date else 'N/A':<15} | "
            f"{t.description[:30]:<15} | "
            f"{t.amount:<10.2f} | "
            f"{t.account_number:<15} | "
            f"{t.transaction_type:<10} | "
            f"{t.category}"
        )

def export_transactions(args) -> None:
    """Export transactions to CSV with optional Qt file dialog."""
    # Build filters from args
    filters = {}
    if hasattr(args, 'start_date') and args.start_date:
        filters["start_date"] = args.start_date
    if hasattr(args, 'end_date') and args.end_date:
        filters["end_date"] = args.end_date
    if hasattr(args, 'limit') and args.limit:
        filters["limit"] = args.limit
    
    # Determine column mode (all_cols is default)
    all_cols = not getattr(args, 'core_cols', False)
    
    # Generate default filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_filename = f"transactions_export_{timestamp}.csv"
    
    # Check if we should use Qt file dialog
    if args.gui and QFileDialog is not None and pd is not None:
        _export_with_qt(filters, default_filename, all_cols)
    else:
        _export_basic(filters, default_filename, all_cols)

def _export_basic(filters: dict, default_filename: str, all_cols: bool) -> None:
    """Basic CSV export without Qt dependencies."""
    output_path = Path.cwd() / default_filename
    
    # Use the db_io_service to export to DataFrame
    df = db_io_service.export_dataframe(all_cols=all_cols, **filters)
    
    if df.empty:
        print("No transactions to export.")
        return
        
    # Save to CSV
    df.to_csv(output_path, index=False)
    
    print(f"Exported {len(df)} transactions to {output_path}")
    print(f"Full path: {output_path.absolute()}")
    print(f"Column mode: {'All columns' if all_cols else 'Core columns only'}")

def _safe_strftime(date_obj, fmt="%d-%m-%Y") -> str:
    """Safely format a date that could be a string or datetime object."""
    if not date_obj:
        return ""
    try:
        if isinstance(date_obj, str):
            # Try to parse the string as a date if it's not already a datetime
            from datetime import datetime
            try:
                # Try common date formats
                for date_fmt in ("%Y-%m-%d", "%d-%m-%Y", "%Y/%m/%d", "%d/%m/%Y"):
                    try:
                        dt = datetime.strptime(date_obj, date_fmt)
                        return dt.strftime(fmt)
                    except ValueError:
                        continue
                # If no format matched, return as is
                return date_obj
            except Exception:
                return date_obj
        elif hasattr(date_obj, 'strftime'):
            return date_obj.strftime(fmt)
        return str(date_obj)
    except Exception as e:
        logger.warning(f"Error formatting date {date_obj}: {e}")
        return str(date_obj) if date_obj else ""

def _export_with_qt(filters: dict, default_filename: str, all_cols: bool) -> None:
    """Export transactions using Qt file dialog."""
    try:
        # Use the db_io_service to export to DataFrame
        df = db_io_service.export_dataframe(all_cols=all_cols, **filters)
        
        if df.empty:
            print("No transactions to export.")
            return
        
        # Create Qt application
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv + ['-platform', 'windows:darkmode=1'])
        
        # Create parent widget for the dialog
        parent = QWidget()
        parent.setWindowTitle("Save CSV")
        
        # Show save dialog
        file_path, _ = QFileDialog.getSaveFileName(
            parent,
            "Save Transactions to CSV",
            str(Path.home() / default_filename),
            "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            # Ensure .csv extension
            if not file_path.lower().endswith(".csv"):
                file_path += ".csv"
            
            # Save to CSV
            df.to_csv(file_path, index=False)
            print(f"Exported {len(df)} transactions to {file_path}")
        else:
            print("Export cancelled")
            
    except Exception as e:
        logger.error(f"Error in Qt export: {e}")
        print(f"Error: {e}")
    finally:
        # Clean up Qt resources
        if 'app' in locals() and app:
            app.quit()

def show_stats(_) -> None:
    """Show database statistics."""
    # Get statistics from db_io_service
    stats = db_io_service.stats()
    
    if stats['total_count'] == 0:
        print("No transactions found in the database.")
        return
    
    # Display statistics
    total_transactions = stats['total_count']
    
    # Get date range from stats
    min_date, max_date = stats['date_range']
    if min_date and max_date:
        date_range = f"{min_date.strftime('%d-%m-%Y')} to {max_date.strftime('%d-%m-%Y')}"
    else:
        date_range = "N/A"
    
    # Get account counts from stats
    account_counts = stats['account_counts']
    
    # Print statistics
    print("\n=== Database Statistics ===")
    print(f"Total transactions: {total_transactions}")
    print(f"Date range: {date_range}")
    print(f"Total amount: ${total_amount:.2f}")
    
    if account_counts:
        print("\nTransactions by account:")
        for account, count in account_counts.items():
            print(f"  - {account}: {count}")
    else:
        print("\nNo account information available.")

def delete_all_transactions(_) -> None:
    """Delete all transactions from the database."""
    confirm = input("Are you sure you want to delete ALL transactions? This cannot be undone. (y/n): ")
    if confirm.lower() == 'y':
        count = db_io_service.delete_all_transactions()
        print(f"Deleted {count} transactions.")
    else:
        print("Operation cancelled.")

def create_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(description="Database query tool")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Common arguments
    common_args = argparse.ArgumentParser(add_help=False)
    common_args.add_argument("--start-date", help="Start date (YYYY-MM-DD)")
    common_args.add_argument("--end-date", help="End date (YYYY-MM-DD)")
    common_args.add_argument("--limit", type=int, help="Limit number of results")
    common_args.add_argument("--gui", action="store_true", help="Use GUI file dialog (requires PySide6)")
    
    # List command
    list_parser = subparsers.add_parser(
        "list", 
        parents=[common_args],
        help="List transactions"
    )
    list_parser.set_defaults(func=list_transactions)
    
    # Export command
    export_parser = subparsers.add_parser(
        "export", 
        parents=[common_args],
        help="Export transactions to CSV"
    )
    
    # Add column selection group to export parser
    col_group = export_parser.add_mutually_exclusive_group()
    col_group.add_argument('--all-cols', '-ac', action='store_true', default=True,
                         help='Export all columns (default)')
    col_group.add_argument('--core-cols', '-cc', action='store_true', 
                         help='Export only core columns')
    
    export_parser.set_defaults(func=export_transactions)
    
    # Stats command
    stats_parser = subparsers.add_parser(
        'stats',
        parents=[common_args],
        help="Show database statistics"
    )
    stats_parser.set_defaults(func=show_stats)
    
    # Delete command
    delete_parser = subparsers.add_parser(
        "delete-all", 
        help="Delete all transactions"
    )
    delete_parser.set_defaults(func=delete_all_transactions)
    
    # Add version
    parser.add_argument(
        '-v', '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )
    
    return parser

def main() -> None:
    """Main entry point for the CLI."""
    try:
        parser = create_parser()
        args = parser.parse_args()
        
        if hasattr(args, 'func'):
            args.func(args)
        else:
            parser.print_help()
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
