"""Data service for accessing the database."""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union

import pandas as pd

from ..repository.transaction_repository import TransactionRepository, Transaction


class DataService:
    """Service for accessing and manipulating data in the database."""
    
    def __init__(self):
        """Initialize the data service."""
        self.transaction_repo = TransactionRepository()
    
    def get_transactions(
        self, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        account_number: Optional[str] = None,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        description_contains: Optional[str] = None,
        tags_contain: Optional[str] = None
    ) -> List[Transaction]:
        """
        Get transactions with optional filtering.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            account_number: Optional account number filter
            min_amount: Optional minimum amount filter
            max_amount: Optional maximum amount filter
            description_contains: Optional description text filter
            tags_contain: Optional tags text filter
            
        Returns:
            List of Transaction objects matching the filters
        """
        # Start with all transactions
        transactions = self.transaction_repo.get_all()
        
        # Apply filters
        if start_date:
            transactions = [t for t in transactions if t.date >= start_date]
        
        if end_date:
            transactions = [t for t in transactions if t.date <= end_date]
        
        if account_number:
            transactions = [t for t in transactions if t.account_number == account_number]
        
        if min_amount is not None:
            transactions = [t for t in transactions if t.amount >= min_amount]
        
        if max_amount is not None:
            transactions = [t for t in transactions if t.amount <= max_amount]
        
        if description_contains:
            transactions = [t for t in transactions if description_contains.lower() in t.description.lower()]
        
        if tags_contain:
            transactions = [t for t in transactions if t.tags and tags_contain.lower() in t.tags.lower()]
        
        return transactions
    
    def get_transactions_as_dataframe(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        account_number: Optional[str] = None,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        description_contains: Optional[str] = None,
        tags_contain: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Get transactions as a pandas DataFrame with optional filtering.
        
        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            account_number: Optional account number filter
            min_amount: Optional minimum amount filter
            max_amount: Optional maximum amount filter
            description_contains: Optional description text filter
            tags_contain: Optional tags text filter
            
        Returns:
            DataFrame of transactions matching the filters
        """
        transactions = self.get_transactions(
            start_date=start_date,
            end_date=end_date,
            account_number=account_number,
            min_amount=min_amount,
            max_amount=max_amount,
            description_contains=description_contains,
            tags_contain=tags_contain
        )
        
        # Convert to DataFrame
        if not transactions:
            return pd.DataFrame()
        
        data = []
        for t in transactions:
            data.append({
                'id': t.id,
                'date': t.date,
                'description': t.description,
                'amount': t.amount,
                'balance': t.balance,
                'account_number': t.account_number,
                'source_bank': t.source_bank,
                'tags': t.tags or ''
            })
        
        return pd.DataFrame(data)
    
    def update_transaction_tags(self, transaction_id: int, tags: str) -> bool:
        """
        Update the tags for a transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            tags: New tags string (comma-separated)
            
        Returns:
            True if successful, False otherwise
        """
        return self.transaction_repo.update_tags(transaction_id, tags)
    
    def get_unique_account_numbers(self) -> List[str]:
        """
        Get a list of unique account numbers in the database.
        
        Returns:
            List of unique account numbers
        """
        transactions = self.transaction_repo.get_all()
        return list(set(t.account_number for t in transactions if t.account_number))
    
    def get_date_range(self) -> tuple:
        """
        Get the min and max dates in the database.
        
        Returns:
            Tuple of (min_date, max_date)
        """
        transactions = self.transaction_repo.get_all()
        if not transactions:
            return (None, None)
        
        dates = [t.date for t in transactions]
        return (min(dates), max(dates))