# Database I/O Service Design

## Overview

This document outlines the design and implementation of the new `DBIOService` in the FlatMate application, with a focus on DataFrame-based operations. The service provides a standardized interface for modules to interact with the database without directly handling SQL queries or database connections.

## Core Requirements

1. **DataFrame-centric API**: Modules should work with pandas DataFrames, not directly with database queries
2. **Column Name Abstraction**: Service should handle conversion between display names and database column names
3. **Consistent Interface**: Standard methods for common operations (get, update, delete)
4. **Separation of Concerns**: Modules should not directly query the database
5. **In-memory Processing**: Changes should be made to DataFrames in memory before being persisted

## Service Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│     Modules     │◄───►│   DBIOService   │◄───►│   Repository    │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                              │
                              │
                        ┌─────▼─────┐
                        │           │
                        │ Utilities │
                        │           │
                        └───────────┘
```

### Key Components

1. **DBIOService**: High-level service providing DataFrame operations
2. **Repository**: Low-level database access (SQLite, etc.)
3. **Utilities**: Column mapping, data conversion, validation

## Core API Methods

### 1. DataFrame Retrieval

```python
def get_transactions_df(self, **kwargs) -> pd.DataFrame:
    """
    Get transactions as a DataFrame with configurable options.
  
    Args:
        date_range: Optional[Tuple[datetime, datetime]] - Start and end dates
        account: Optional[str] - Account number filter
        col_format: str = 'display' - Column format ('display', 'db', 'raw')
        col_set: str = 'default' - Column set ('default', 'all', 'core_only')
  
    Returns:
        DataFrame containing transaction data
    """
```

### 2. DataFrame Updates

```python
def update_transactions(self, df: pd.DataFrame) -> Dict[str, Any]:
    """
    Update transactions from a DataFrame.
  
    Args:
        df: DataFrame containing transaction data to update
      
    Returns:
        Dictionary with update statistics:
        {
            'updated_count': int,
            'error_count': int,
            'errors': List[str]
        }
    """
```

### 3. Column Format Conversion

```python
def convert_columns(self, df: pd.DataFrame, to_format: str = 'display') -> pd.DataFrame:
    """
    Convert DataFrame columns between formats.
  
    Args:
        df: DataFrame to convert
        to_format: Target format ('display', 'db', 'raw')
      
    Returns:
        DataFrame with converted column names
    """
```

## Implementation Example

```python
class DBIOService:
    """High-level service for database I/O operations with DataFrame support."""
  
    def __init__(self, repo: Optional[SQLiteTransactionRepository] = None):
        """
        Initialize the DB I/O Service.
      
        Args:
            repo: Optional repository instance for dependency injection.
                 If None, a new SQLiteTransactionRepository will be created.
        """
        self.repo = repo or SQLiteTransactionRepository()
        self._column_mapper = ColumnMapper()
  
    def get_transactions_df(self, **kwargs) -> pd.DataFrame:
        """
        Get transactions as a DataFrame with configurable options.
      
        Args:
            date_range: Optional tuple of (start_date, end_date)
            account: Optional account number filter
            col_format: Column format ('display', 'db', 'raw'), default='display'
            col_set: Column set to include ('default', 'all', 'minimal'), default='default'
          
        Returns:
            DataFrame containing transaction data
        """
        # Extract parameters with defaults
        date_range = kwargs.get('date_range', None)
        account = kwargs.get('account', None)
        col_format = kwargs.get('col_format', 'display')
        col_set = kwargs.get('col_set', 'default')
      
        # Build filters for repository
        filters = {}
        if date_range:
            filters['start_date'] = date_range[0]
            filters['end_date'] = date_range[1]
        if account:
            filters['account_number'] = account
          
        # Get transactions from repository
        transactions = self.repo.get_transactions(filters)
      
        # Convert to DataFrame
        if not transactions:
            # Return empty DataFrame with appropriate columns
            columns = self._get_columns_for_set(col_set, col_format)
            return pd.DataFrame(columns=columns)
          
        # Convert transactions to DataFrame
        df = pd.DataFrame([t.__dict__ for t in transactions])
      
        # Apply column set filtering
        df = self._filter_columns_by_set(df, col_set)
      
        # Apply column format conversion
        if col_format != 'raw':
            df = self._convert_column_format(df, col_format)
          
        return df
  
    def update_transactions(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Update transactions from a DataFrame.
      
        Args:
            df: DataFrame containing transaction data to update
          
        Returns:
            Dictionary with update statistics
        """
        if df.empty:
            return {
                'updated_count': 0,
                'error_count': 0,
                'errors': []
            }
          
        # Ensure DataFrame has ID column
        if 'id' not in df.columns:
            return {
                'updated_count': 0,
                'error_count': len(df),
                'errors': ['DataFrame missing ID column']
            }
          
        # Convert display column names to database column names if needed
        db_df = self._ensure_db_column_format(df)
      
        # Track statistics
        stats = {
            'updated_count': 0,
            'error_count': 0,
            'errors': []
        }
      
        # Process updates
        for _, row in db_df.iterrows():
            transaction_id = row['id']
          
            # Extract fields to update (excluding id)
            update_data = {col: row[col] for col in db_df.columns if col != 'id'}
          
            # Update via repository
            try:
                success = self.repo.update_transaction(transaction_id, update_data)
                if success:
                    stats['updated_count'] += 1
                else:
                    stats['error_count'] += 1
                    stats['errors'].append(f"Failed to update transaction {transaction_id}")
            except Exception as e:
                stats['error_count'] += 1
                stats['errors'].append(f"Error updating transaction {transaction_id}: {str(e)}")
              
        return stats
  
    def _ensure_db_column_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Ensure DataFrame columns are in database format.
      
        Args:
            df: Input DataFrame
          
        Returns:
            DataFrame with database column names
        """
        # Check if already in database format
        if self._is_db_column_format(df):
            return df.copy()
          
        # Convert to database format
        return self._convert_column_format(df, 'db')
  
    def _is_db_column_format(self, df: pd.DataFrame) -> bool:
        """
        Check if DataFrame is already in database column format.
      
        Args:
            df: DataFrame to check
          
        Returns:
            True if in database format, False otherwise
        """
        # Implementation depends on how you detect column format
        # This is a simplified example
        db_columns = self._column_mapper.get_db_columns()
        return all(col in db_columns for col in df.columns if col in self._column_mapper.get_mappable_columns())
  
    def _convert_column_format(self, df: pd.DataFrame, format_type: str) -> pd.DataFrame:
        """
        Convert DataFrame columns to specified format.
      
        Args:
            df: DataFrame to convert
            format_type: Target format ('display', 'db')
          
        Returns:
            DataFrame with converted column names
        """
        if df.empty:
            return df.copy()
          
        result_df = df.copy()
      
        if format_type == 'display':
            # Convert from DB to display names
            mapping = self._column_mapper.get_db_to_display_mapping()
        elif format_type == 'db':
            # Convert from display to DB names
            mapping = self._column_mapper.get_display_to_db_mapping()
        else:
            # Unknown format, return as is
            return result_df
          
        # Apply mapping to columns that exist in the DataFrame
        rename_dict = {old: new for old, new in mapping.items() if old in result_df.columns}
        if rename_dict:
            result_df = result_df.rename(columns=rename_dict)
          
        return result_df
  
    def _filter_columns_by_set(self, df: pd.DataFrame, col_set: str) -> pd.DataFrame:
        """
        Filter DataFrame columns based on column set.
      
        Args:
            df: DataFrame to filter
            col_set: Column set name ('default', 'all', 'minimal')
          
        Returns:
            DataFrame with filtered columns
        """
        if df.empty or col_set == 'all':
            return df.copy()
          
        # Get columns for the specified set
        columns = self._get_columns_for_set(col_set, 'raw')
      
        # Filter DataFrame to include only specified columns that exist
        existing_columns = [col for col in columns if col in df.columns]
      
        return df[existing_columns].copy()
  
    def _get_columns_for_set(self, col_set: str, format_type: str) -> List[str]:
        """
        Get column names for a specific set and format.
      
        Args:
            col_set: Column set name ('default', 'all', 'minimal')
            format_type: Column format ('display', 'db', 'raw')
          
        Returns:
            List of column names
        """
        # Define column sets (these could be moved to configuration)
        sets = {
            'minimal': ['id', 'date', 'description', 'amount', 'account_number'],
            'default': ['id', 'date', 'description', 'amount', 'account_number', 'category', 'tags', 'notes'],
            # 'all' is handled by returning all columns
        }
      
        # Get raw column names for the set
        if col_set not in sets:
            col_set = 'default'  # Default to 'default' if invalid set name
          
        raw_columns = sets[col_set]
      
        # Convert to requested format if needed
        if format_type == 'raw':
            return raw_columns
        elif format_type == 'display':
            return [self._column_mapper.db_to_display(col) for col in raw_columns]
        elif format_type == 'db':
            return [self._column_mapper.display_to_db(col) for col in raw_columns]
        else:
            return raw_columns  # Default to raw if unknown format
```

## Column Mapper Utility

```python
class ColumnMapper:
    """Utility for mapping between display and database column names."""
  
    def __init__(self):
        """Initialize with standard column mappings."""
        # Define mappings between display names and database names
        self._display_to_db = {
            'Date': 'date',
            'Description': 'details',
            'Amount': 'amount',
            'Account': 'account',
            'Balance': 'balance',
            'Category': 'category',
            'Tags': 'tags',
            'Notes': 'notes',
            # Add more mappings as needed
        }
      
        # Create reverse mapping
        self._db_to_display = {db: display for display, db in self._display_to_db.items()}
      
        # Always keep these columns unmapped
        self._unmapped_columns = ['id', 'import_date', 'modified_date']
  
    def display_to_db(self, column: str) -> str:
        """
        Convert display column name to database column name.
      
        Args:
            column: Display column name
          
        Returns:
            Database column name
        """
        if column in self._unmapped_columns:
            return column
        return self._display_to_db.get(column, column)
  
    def db_to_display(self, column: str) -> str:
        """
        Convert database column name to display column name.
      
        Args:
            column: Database column name
          
        Returns:
            Display column name
        """
        if column in self._unmapped_columns:
            return column
        return self._db_to_display.get(column, column)
  
    def get_display_to_db_mapping(self) -> Dict[str, str]:
        """Get the full display to database mapping dictionary."""
        return self._display_to_db.copy()
  
    def get_db_to_display_mapping(self) -> Dict[str, str]:
        """Get the full database to display mapping dictionary."""
        return self._db_to_display.copy()
  
    def get_db_columns(self) -> List[str]:
        """Get all known database column names."""
        return list(self._db_to_display.keys()) + self._unmapped_columns
  
    def get_display_columns(self) -> List[str]:
        """Get all known display column names."""
        return list(self._display_to_db.keys()) + self._unmapped_columns
  
    def get_mappable_columns(self) -> List[str]:
        """Get all columns that can be mapped (excluding unmapped columns)."""
        return list(self._display_to_db.keys()) + list(self._db_to_display.keys())
```

## Usage Examples

### Example 1: Loading Transactions for Display

```python
# In a module presenter
def load_transactions(self, filters=None):
    """Load transactions from database with optional filters."""
    try:
        # Get transactions as DataFrame with display column names
        df = self.data_service.get_transactions_df(
            date_range=filters.get('date_range'),
            account=filters.get('account'),
            col_format='display',  # Use display column names
            col_set='default'      # Use default column set
        )
      
        # DataFrame is ready for display - no conversion needed
        self.view.update_transactions(df)
    except Exception as e:
        log(f"Error loading transactions: {str(e)}", level="error")
```

### Example 2: Updating Transactions

```python
# In a module presenter
def save_changes(self, df: pd.DataFrame):
    """Save changes made to transactions."""
    try:
        # Update transactions in database
        result = self.data_service.update_transactions(df)
      
        if result['error_count'] > 0:
            log(f"Errors updating transactions: {result['errors']}", level="warning")
          
        return result['updated_count'] > 0
    except Exception as e:
        log(f"Error saving changes: {str(e)}", level="error")
        return False
```

## Pros and Cons

### Pros

1. **Clean Separation of Concerns**

   - Modules work with DataFrames, not SQL queries
   - Service handles all database interactions
   - Column mapping is centralized
2. **Consistent Interface**

   - Standardized methods for common operations
   - Configurable column formats and sets
   - Error handling and reporting
3. **Improved Testability**

   - Service can be mocked for testing
   - DataFrames are easy to create and manipulate in tests
   - Repository can be injected for testing
4. **Flexibility**

   - Support for different column formats
   - Configurable column sets
   - Easy to extend with new operations

### Cons

1. **Performance Overhead**

   - Converting between DataFrames and database models
   - Multiple transformations for column names
   - Potential memory usage for large datasets
2. **Learning Curve**

   - New API for developers to learn
   - Understanding column mapping concepts
   - Configuration options may be confusing initially
3. **Maintenance Complexity**

   - Keeping column mappings in sync
   - Handling schema changes
   - Managing different column formats

## Recommendations

1. **Implement Core DataFrame Methods First**

   - `get_transactions_df()` - Retrieval with filtering and column formatting
   - `update_transactions()` - Batch updates from DataFrame
2. **Use Dependency Injection**

   - Allow repository to be injected for testing
   - Consider a service locator pattern for modules
3. **Centralize Column Mapping**

   - Create a dedicated `ColumnMapper` utility
   - Use StandardColumns as the source of truth
   - Support multiple column formats (display, database, raw)
4. **Add Batch Operations**

   - Support updating multiple transactions in a single call
   - Implement transaction batching for performance
   - Add progress reporting for large operations
5. **Implement Caching Strategy**

   - Cache frequently used data
   - Implement cache invalidation
   - Consider memory usage for large datasets
6. **Add Validation**

   - Validate DataFrame schema before updates
   - Enforce business rules at the service layer
   - Provide detailed error reporting
7. **Document API Thoroughly**

   - Clear method signatures and docstrings
   - Usage examples for common operations
   - Configuration options and defaults

## Conclusion

The proposed `DBIOService` design provides a clean, DataFrame-centric API for modules to interact with the database. By centralizing column mapping and database operations, it simplifies module development and improves maintainability. The service layer abstracts away the complexities of database access, allowing modules to focus on business logic rather than data access details.

This design aligns with the requirements to work with DataFrames, handle column name conversions, and separate modules from direct database queries. The implementation examples provide a starting point for development, with flexibility to adapt to specific needs as the application evolves.
