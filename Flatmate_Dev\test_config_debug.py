#!/usr/bin/env python3
"""
Debug script to test the categorize config system.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent / "flatmate" / "src"
sys.path.insert(0, str(project_root))

def test_config():
    print("=== Testing Categorize Config ===")
    
    try:
        # Import the config
        from fm.modules.categorize.config.config import config
        print(f"✅ Config imported successfully: {type(config)}")
        print(f"   Module name: {config.MODULE_NAME}")
        
        # Test ensure_defaults
        print("\n--- Testing ensure_defaults ---")
        test_defaults = {
            'test.key1': 'value1',
            'test.key2': 42,
            'categorize.test.key3': True
        }
        
        config.ensure_defaults(test_defaults)
        print("✅ ensure_defaults called successfully")
        
        # Test get_value
        print("\n--- Testing get_value ---")
        for key, expected in test_defaults.items():
            actual = config.get_value(key)
            print(f"   {key}: {actual} (expected: {expected})")
            if actual == expected:
                print("   ✅ Value matches")
            else:
                print("   ❌ Value mismatch!")
        
        # Test key origins
        print("\n--- Testing key origins ---")
        origins = config.get_key_origins()
        print(f"   Total keys tracked: {len(origins)}")
        for key, info in origins.items():
            print(f"   {key}: {info['final_value']} from {info['source']}")
        
        # Test YAML generation
        print("\n--- Testing YAML generation ---")
        yaml_content = config.generate_documented_yaml()
        print("✅ YAML generated successfully")
        print("First 200 chars of YAML:")
        print(yaml_content[:200] + "...")
        
        # Test saving YAML
        print("\n--- Testing YAML save ---")
        saved_path = config.save_defaults_yaml("test_defaults.yaml")
        print(f"✅ YAML saved to: {saved_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)
