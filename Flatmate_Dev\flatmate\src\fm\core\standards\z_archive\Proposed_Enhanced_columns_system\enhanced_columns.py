"""
Enhanced StandardColumns implementation with comprehensive column mapping.

This module replaces the simple enum-based approach with a rich column definition
system that handles all naming variants and user preferences.
"""

from enum import Enum
from typing import Dict, List, Optional, Set
from .column_definition import (
    ColumnDefinition, 
    DataType, 
    create_core_column, 
    create_user_column, 
    create_metadata_column
)


class EnhancedStandardColumns(Enum):
    """
    Enhanced column definitions with complete mapping between all naming variants.
    
    This enum serves as the single source of truth for all column-related operations
    across the Flatmate application. Each enum value contains a complete ColumnDefinition
    that maps between database names, Transaction field names, and display names.
    """
    
    # Core Transaction Fields (Required)
    DATE = create_core_column(
        key="transaction_date",
        db_name="date",
        field_name="date",
        display_name="Date",
        data_type=DataType.DATE,
        width=12,
        sort_order=1,
        required=True
    )
    
    DETAILS = create_core_column(
        key="transaction_details",
        db_name="details",
        field_name="description",  # Maps Transaction.description -> db.details
        display_name="Details",  # Match StandardColumns for consistency
        data_type=DataType.TEXT,
        width=40,
        sort_order=2,
        required=True
    )
    
    AMOUNT = create_core_column(
        key="transaction_amount",
        db_name="amount",
        field_name="amount",
        display_name="Amount",
        data_type=DataType.REAL,
        width=12,
        sort_order=3,
        required=True
    )
    
    ACCOUNT = create_core_column(
        key="transaction_account",
        db_name="account",
        field_name="account_number",  # Maps Transaction.account_number -> db.account
        display_name="Account",
        data_type=DataType.TEXT,
        width=15,
        sort_order=4,
        required=True
    )
    
    # User-Editable Fields
    TAGS = create_user_column(
        key="transaction_tags",
        db_name="tags",
        field_name="tags",
        display_name="Tags",
        width=20,
        sort_order=5,
        editable=True
    )
    
    CATEGORY = create_user_column(
        key="transaction_category",
        db_name="category", 
        field_name="category",
        display_name="Category",
        width=20,
        sort_order=6,
        editable=True
    )
    
    NOTES = create_user_column(
        key="transaction_notes",
        db_name="notes",
        field_name="notes",
        display_name="Notes",
        width=30,
        sort_order=7,
        editable=True
    )
    
    # Additional Core Fields
    BALANCE = create_core_column(
        key="transaction_balance",
        db_name="balance",
        field_name="balance",
        display_name="Balance",
        data_type=DataType.REAL,
        width=12,
        sort_order=8
    )
    
    UNIQUE_ID = create_core_column(
        key="transaction_unique_id",
        db_name="unique_id",
        field_name="unique_id",
        display_name="Unique ID",
        data_type=DataType.TEXT,
        width=15,
        sort_order=9
    )
    
    # Amount Breakdown Fields
    CREDIT_AMOUNT = create_core_column(
        key="transaction_credit",
        db_name="credit_amount",
        field_name="credit_amount",
        display_name="Credit",
        data_type=DataType.REAL,
        width=12,
        sort_order=10
    )
    
    DEBIT_AMOUNT = create_core_column(
        key="transaction_debit",
        db_name="debit_amount",
        field_name="debit_amount",
        display_name="Debit",
        data_type=DataType.REAL,
        width=12,
        sort_order=11
    )
    
    # Transaction Type and Payment Info
    PAYMENT_TYPE = create_core_column(
        key="transaction_payment_type",
        db_name="payment_type",
        field_name="transaction_type",  # Maps Transaction.transaction_type -> db.payment_type
        display_name="Payment Type",
        width=15,
        sort_order=12
    )
    
    # Source Information
    SOURCE_FILENAME = create_metadata_column(
        key="transaction_source_file",
        db_name="source_filename",
        field_name="source_file",  # Maps Transaction.source_file -> db.source_filename
        display_name="Source File",
        width=20,
        sort_order=20
    )
    
    SOURCE_BANK = create_metadata_column(
        key="transaction_source_bank",
        db_name="source_bank",
        field_name="source_bank",
        display_name="Source Bank",
        width=15,
        sort_order=21
    )
    
    # System Fields
    IMPORT_DATE = create_metadata_column(
        key="system_import_date",
        db_name="import_date",
        field_name="import_date",
        display_name="Import Date",
        data_type=DataType.DATETIME,
        width=15,
        sort_order=30
    )
    
    MODIFIED_DATE = create_metadata_column(
        key="system_modified_date",
        db_name="modified_date",
        field_name="modified_date",
        display_name="Modified Date",
        data_type=DataType.DATETIME,
        width=15,
        sort_order=31
    )
    
    # Properties for easy access
    @property
    def definition(self) -> ColumnDefinition:
        """Get the complete column definition."""
        return self.value
    
    @property
    def db_name(self) -> str:
        """Database column name."""
        return self.value.db_name
    
    @property
    def field_name(self) -> str:
        """Transaction dataclass field name."""
        return self.value.field_name
    
    @property
    def display_name(self) -> str:
        """Current display name (user preference or default)."""
        return self.value.display_name
    
    @property
    def is_editable(self) -> bool:
        """Whether this column can be edited by users."""
        return self.value.is_editable
    
    @property
    def is_required(self) -> bool:
        """Whether this column must be visible."""
        return self.value.is_required
    
    # Class methods for mapping operations
    @classmethod
    def get_field_to_db_mapping(cls) -> Dict[str, str]:
        """Map Transaction field names to database column names."""
        return {col.value.field_name: col.value.db_name for col in cls}
    
    @classmethod
    def get_db_to_field_mapping(cls) -> Dict[str, str]:
        """Map database column names to Transaction field names."""
        return {col.value.db_name: col.value.field_name for col in cls}
    
    @classmethod
    def get_db_to_display_mapping(cls) -> Dict[str, str]:
        """Map database column names to display names."""
        return {col.value.db_name: col.display_name for col in cls}
    
    @classmethod
    def get_display_to_db_mapping(cls) -> Dict[str, str]:
        """Map display names to database column names."""
        return {col.display_name: col.value.db_name for col in cls}
    
    @classmethod
    def get_default_column_order(cls) -> List['EnhancedStandardColumns']:
        """Get columns in default display order."""
        return sorted(cls, key=lambda x: x.value.sort_order)
    
    @classmethod
    def get_required_columns(cls) -> List['EnhancedStandardColumns']:
        """Get columns that must be visible."""
        return [col for col in cls if col.value.is_required]
    
    @classmethod
    def get_editable_columns(cls) -> List['EnhancedStandardColumns']:
        """Get columns that can be edited by users."""
        return [col for col in cls if col.value.is_editable]
    
    @classmethod
    def find_by_db_name(cls, db_name: str) -> Optional['EnhancedStandardColumns']:
        """Find column by database name."""
        for col in cls:
            if col.value.db_name == db_name:
                return col
        return None
    
    @classmethod
    def find_by_field_name(cls, field_name: str) -> Optional['EnhancedStandardColumns']:
        """Find column by Transaction field name."""
        for col in cls:
            if col.value.field_name == field_name:
                return col
        return None
    
    @classmethod
    def find_by_display_name(cls, display_name: str) -> Optional['EnhancedStandardColumns']:
        """Find column by display name."""
        for col in cls:
            if col.display_name == display_name:
                return col
        return None


# Backward compatibility alias
StandardColumns = EnhancedStandardColumns
