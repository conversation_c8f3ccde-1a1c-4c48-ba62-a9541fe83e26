"""
Centralized date formatting service.

This service provides the elegant solution for database vs display date formatting
throughout the application. It handles conversion between storage format (ISO) 
and user-friendly display formats (configurable by locale/preference).
"""

import pandas as pd
from datetime import datetime, date
from typing import Union, Optional, List
from enum import Enum


class DateDisplayFormat(Enum):
    """Standard date display formats for different locales."""
    
    # New Zealand / UK / Australia style
    DD_MM_YYYY = "DD/MM/YYYY"
    DD_MM_YY = "DD/MM/YY"
    
    # US style
    MM_DD_YYYY = "MM/DD/YYYY"
    MM_DD_YY = "MM/DD/YY"
    
    # ISO style (same as database)
    YYYY_MM_DD = "YYYY-MM-DD"
    
    # European style
    DD_MM_YYYY_DOTS = "DD.MM.YYYY"
    
    # Verbose styles
    DD_MMM_YYYY = "DD MMM YYYY"  # 25 Dec 2023
    MMM_DD_YYYY = "MMM DD, YYYY"  # Dec 25, 2023


class DateFormatService:
    """
    Centralized date formatting service for the entire application.
    
    This service eliminates the need for scattered date formatting logic by providing
    a single, consistent way to handle date conversions between database format
    and user-friendly display formats.
    """
    
    # Default format for NZ users
    DEFAULT_DISPLAY_FORMAT = DateDisplayFormat.DD_MM_YYYY
    
    @staticmethod
    def get_user_date_format() -> DateDisplayFormat:
        """
        Get the user's preferred date display format from config.
        
        Returns:
            DateDisplayFormat enum value
        """
        try:
            from fm.core.config.config import config
            format_str = config.get_value('display.date_format', DateFormatService.DEFAULT_DISPLAY_FORMAT.value)
            
            # Convert string back to enum
            for fmt in DateDisplayFormat:
                if fmt.value == format_str:
                    return fmt
            
            # Fallback to default if invalid format in config
            return DateFormatService.DEFAULT_DISPLAY_FORMAT
        except:
            # Fallback if config not available
            return DateFormatService.DEFAULT_DISPLAY_FORMAT
    
    @staticmethod
    def format_date_for_display(date_value: Union[str, datetime, date, pd.Timestamp], 
                              format_override: Optional[DateDisplayFormat] = None) -> str:
        """
        Convert a date from database format to user-friendly display format.
        
        Args:
            date_value: Date in database format (ISO string, datetime, etc.)
            format_override: Optional specific format to use instead of user preference
            
        Returns:
            Formatted date string for display
        """
        if pd.isna(date_value) or date_value is None:
            return ""
        
        # Convert to datetime object
        try:
            if isinstance(date_value, str):
                # Parse ISO format from database
                dt = pd.to_datetime(date_value)
            elif isinstance(date_value, (datetime, date, pd.Timestamp)):
                dt = pd.to_datetime(date_value)
            else:
                return str(date_value)  # Fallback
        except:
            return str(date_value)  # Fallback for unparseable dates
        
        # Get format to use
        display_format = format_override or DateFormatService.get_user_date_format()
        
        # Convert to display format
        return DateFormatService._apply_date_format(dt, display_format)
    
    @staticmethod
    def _apply_date_format(dt: pd.Timestamp, display_format: DateDisplayFormat) -> str:
        """Apply the specified date format to a datetime object."""
        try:
            if display_format == DateDisplayFormat.DD_MM_YYYY:
                return dt.strftime("%d/%m/%Y")
            elif display_format == DateDisplayFormat.DD_MM_YY:
                return dt.strftime("%d/%m/%y")
            elif display_format == DateDisplayFormat.MM_DD_YYYY:
                return dt.strftime("%m/%d/%Y")
            elif display_format == DateDisplayFormat.MM_DD_YY:
                return dt.strftime("%m/%d/%y")
            elif display_format == DateDisplayFormat.YYYY_MM_DD:
                return dt.strftime("%Y-%m-%d")
            elif display_format == DateDisplayFormat.DD_MM_YYYY_DOTS:
                return dt.strftime("%d.%m.%Y")
            elif display_format == DateDisplayFormat.DD_MMM_YYYY:
                return dt.strftime("%d %b %Y")
            elif display_format == DateDisplayFormat.MMM_DD_YYYY:
                return dt.strftime("%b %d, %Y")
            else:
                # Fallback to ISO format
                return dt.strftime("%Y-%m-%d")
        except:
            # Fallback for any formatting errors
            return str(dt.date()) if hasattr(dt, 'date') else str(dt)
    
    @staticmethod
    def format_dataframe_dates(df: pd.DataFrame, date_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Format all date columns in a DataFrame for display.
        
        Args:
            df: DataFrame with date columns in database format
            date_columns: Optional list of column names to format. If None, auto-detects date columns.
            
        Returns:
            New DataFrame with formatted date columns
        """
        if df.empty:
            return df.copy()
        
        display_df = df.copy()
        
        # Auto-detect date columns if not specified
        if date_columns is None:
            date_columns = []
            for col in df.columns:
                # Look for common date column names
                col_lower = str(col).lower()
                if any(date_word in col_lower for date_word in ['date', 'time', 'created', 'modified', 'updated']):
                    # Check if the column actually contains date-like data
                    sample_value = df[col].dropna().iloc[0] if not df[col].dropna().empty else None
                    if sample_value is not None:
                        try:
                            pd.to_datetime(sample_value)
                            date_columns.append(col)
                        except:
                            pass  # Not a date column
        
        # Format each date column
        for col in date_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(DateFormatService.format_date_for_display)
        
        return display_df
    
    @staticmethod
    def parse_display_date_to_database(date_str: str, 
                                     input_format: Optional[DateDisplayFormat] = None) -> Optional[str]:
        """
        Parse a user-entered date string back to database format (ISO).
        
        Args:
            date_str: Date string in display format
            input_format: Optional format hint. If None, tries to auto-detect.
            
        Returns:
            ISO format date string for database storage, or None if unparseable
        """
        if not date_str or pd.isna(date_str):
            return None
        
        date_str = str(date_str).strip()
        if not date_str:
            return None
        
        # Try to parse with specified format first
        if input_format:
            try:
                dt = DateFormatService._parse_with_format(date_str, input_format)
                if dt:
                    return dt.strftime("%Y-%m-%d")
            except:
                pass
        
        # Try common formats
        common_formats = [
            DateDisplayFormat.DD_MM_YYYY,
            DateDisplayFormat.MM_DD_YYYY,
            DateDisplayFormat.YYYY_MM_DD,
            DateDisplayFormat.DD_MM_YY,
            DateDisplayFormat.MM_DD_YY,
        ]
        
        for fmt in common_formats:
            try:
                dt = DateFormatService._parse_with_format(date_str, fmt)
                if dt:
                    return dt.strftime("%Y-%m-%d")
            except:
                continue
        
        # Last resort: let pandas try to parse it
        try:
            dt = pd.to_datetime(date_str)
            return dt.strftime("%Y-%m-%d")
        except:
            return None
    
    @staticmethod
    def _parse_with_format(date_str: str, date_format: DateDisplayFormat) -> Optional[datetime]:
        """Parse date string with specific format."""
        format_map = {
            DateDisplayFormat.DD_MM_YYYY: "%d/%m/%Y",
            DateDisplayFormat.DD_MM_YY: "%d/%m/%y",
            DateDisplayFormat.MM_DD_YYYY: "%m/%d/%Y",
            DateDisplayFormat.MM_DD_YY: "%m/%d/%y",
            DateDisplayFormat.YYYY_MM_DD: "%Y-%m-%d",
            DateDisplayFormat.DD_MM_YYYY_DOTS: "%d.%m.%Y",
            DateDisplayFormat.DD_MMM_YYYY: "%d %b %Y",
            DateDisplayFormat.MMM_DD_YYYY: "%b %d, %Y",
        }
        
        format_str = format_map.get(date_format)
        if format_str:
            return datetime.strptime(date_str, format_str)
        return None


# Convenience functions for backward compatibility
def format_date_for_display(date_value: Union[str, datetime, date, pd.Timestamp]) -> str:
    """Convenience function for formatting a single date."""
    return DateFormatService.format_date_for_display(date_value)


def format_dataframe_dates(df: pd.DataFrame, date_columns: Optional[List[str]] = None) -> pd.DataFrame:
    """Convenience function for formatting DataFrame dates."""
    return DateFormatService.format_dataframe_dates(df, date_columns)


def set_user_date_format(date_format: DateDisplayFormat):
    """
    Set the user's preferred date format in config.

    Args:
        date_format: DateDisplayFormat enum value

    Example:
        set_user_date_format(DateDisplayFormat.DD_MM_YYYY)  # NZ style
        set_user_date_format(DateDisplayFormat.MM_DD_YYYY)  # US style
    """
    try:
        from fm.core.config.config import config
        config.set_value('display.date_format', date_format.value)
    except:
        pass  # Fail silently if config not available
