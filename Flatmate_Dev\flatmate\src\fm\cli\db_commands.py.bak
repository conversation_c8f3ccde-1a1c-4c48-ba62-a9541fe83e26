"""Database command-line interface commands."""
import argparse
import csv
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Qt imports for file dialogs
try:
    from PySide6.QtWidgets import QApplication, QFileDialog, QWidget
except ImportError:
    QApplication = None
    QFileDialog = None
    QWidget = None
    logger = logging.getLogger(__name__)
    logger.warning("PySide6 not available. Some features may be limited.")

# Pandas for data manipulation
try:
    import pandas as pd
except ImportError:
    pd = None
    logger = logging.getLogger(__name__)
    logger.warning("pandas not available. CSV export will use basic writer.")

# Set up basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

try:
    # Import from installed package
    from fm.database_service.repository.transaction_repository import Transaction
    from fm.database_service.repository.sqlite_repository import SQLiteTransactionRepository
    
    # Try to import StandardColumns, but make it optional
    try:
        from fm.core.standards.fm_standard_columns import StandardColumns
    except ImportError:
        logger.warning("Could not import StandardColumns, some features may be limited")
        StandardColumns = None
        
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    logger.error("Please ensure the package is properly installed in development mode")
    logger.error("Run: pip install -e . from the project root")
    sys.exit(1)

def get_transactions(args) -> List[Transaction]:
    """Retrieve transactions based on command line arguments."""
    repo = SQLiteTransactionRepository()
    
    filters = {}
    if hasattr(args, 'start_date') and args.start_date:
        filters["start_date"] = args.start_date
    if hasattr(args, 'end_date') and args.end_date:
        filters["end_date"] = args.end_date
    if hasattr(args, 'limit') and args.limit:
        filters["limit"] = args.limit
    
    return repo.get_transactions(**filters)

def list_transactions(args) -> None:
    """List transactions to the console."""
    transactions = get_transactions(args)
    if not transactions:
        print("No transactions found.")
        return
    
    # Print header
    headers = [
        "Date",
        "Description",
        "Amount",
        "Account",
        "Type",
        "Category"
    ]
    print("\n" + " | ".join(f"{h:<15}" for h in headers))
    print("-" * 100)
    
    # Print transactions
    for t in transactions:
        print(
            f"{t.date.strftime('%d-%m-%Y') if t.date else 'N/A':<15} | "
            f"{t.description[:30]:<15} | "
            f"{t.amount:<10.2f} | "
            f"{t.account_number:<15} | "
            f"{t.transaction_type:<10} | "
            f"{t.category}"
        )

def export_transactions(args) -> None:
    """Export transactions to CSV with optional Qt file dialog."""
    transactions = get_transactions(args)
    if not transactions:
        print("No transactions to export.")
        return
    
    # Generate default filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_filename = f"transactions_export_{timestamp}.csv"
    
    # Check if we should use Qt file dialog
    if args.gui and QFileDialog is not None and pd is not None:
        _export_with_qt(transactions, default_filename)
    else:
        _export_basic(transactions, default_filename)

def _export_basic(transactions: list, default_filename: str) -> None:
    """Basic CSV export without Qt dependencies."""
    output_path = Path.cwd() / default_filename
    
    # Prepare data
    data = []
    for t in transactions:
        data.append({
            "Date": _safe_strftime(getattr(t, 'date', None)),
            "Description": getattr(t, 'description', ''),
            "Amount": getattr(t, 'amount', ''),
            "Account": getattr(t, 'account_number', ''),
            "Type": getattr(t, 'transaction_type', ''),
            "Category": getattr(t, 'category', ''),
            "Source File": getattr(t, 'source_file', ''),
            "Import Date": _safe_strftime(getattr(t, 'import_date', None)),
        })
    
    # Write to CSV
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            if not data:
                return
                
            fieldnames = list(data[0].keys())
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
            
        print(f"Exported {len(transactions)} transactions to {output_path}")
        print(f"Full path: {output_path.absolute()}")
        
    except Exception as e:
        logger.error(f"Error exporting transactions: {e}")
        print(f"Error: {e}")

def _safe_strftime(date_obj, fmt="%d-%m-%Y") -> str:
    """Safely format a date that could be a string or datetime object."""
    if not date_obj:
        return ""
    try:
        if isinstance(date_obj, str):
            # Try to parse the string as a date if it's not already a datetime
            from datetime import datetime
            try:
                # Try common date formats
                for date_fmt in ("%Y-%m-%d", "%d-%m-%Y", "%Y/%m/%d", "%d/%m/%Y"):
                    try:
                        dt = datetime.strptime(date_obj, date_fmt)
                        return dt.strftime(fmt)
                    except ValueError:
                        continue
                # If no format matched, return as is
                return date_obj
            except Exception:
                return date_obj
        elif hasattr(date_obj, 'strftime'):
            return date_obj.strftime(fmt)
        return str(date_obj)
    except Exception as e:
        logger.warning(f"Error formatting date {date_obj}: {e}")
        return str(date_obj) if date_obj else ""

def _export_with_qt(transactions: list, default_filename: str) -> None:
    """Export transactions using Qt file dialog."""
    try:
        # Convert transactions to DataFrame
        data = []
        for t in transactions:
            data.append({
                "Date": _safe_strftime(getattr(t, 'date', None)),
                "Description": getattr(t, 'description', ''),
                "Amount": getattr(t, 'amount', ''),
                "Account": getattr(t, 'account_number', ''),
                "Type": getattr(t, 'transaction_type', ''),
                "Category": getattr(t, 'category', ''),
                "Source File": getattr(t, 'source_file', ''),
                "Tags": getattr(t, 'tags', ''),
                "Source Bank": getattr(t, 'source_bank', ''),
                "Import Date": _safe_strftime(getattr(t, 'import_date', None)),
                "Modified Date": _safe_strftime(getattr(t, 'modified_date', None)),
                "Transaction ID": getattr(t, 'transaction_id', ''),
            })
        
        df = pd.DataFrame(data)
        
        # Create Qt application
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv + ['-platform', 'windows:darkmode=1'])
        
        # Create parent widget for the dialog
        parent = QWidget()
        parent.setWindowTitle("Save CSV")
        
        # Show save dialog
        file_path, _ = QFileDialog.getSaveFileName(
            parent,
            "Save Transactions to CSV",
            str(Path.home() / default_filename),
            "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            # Ensure .csv extension
            if not file_path.lower().endswith(".csv"):
                file_path += ".csv"
            
            # Save to CSV
            df.to_csv(file_path, index=False)
            print(f"Exported {len(transactions)} transactions to {file_path}")
        else:
            print("Export cancelled")
            
    except Exception as e:
        logger.error(f"Error in Qt export: {e}")
        print(f"Error: {e}")
    finally:
        # Clean up Qt resources
        if 'app' in locals() and app:
            app.quit()

def show_stats(_) -> None:
    """Show database statistics."""
    repo = SQLiteTransactionRepository()
    
    # Get all transactions
    transactions = repo.get_transactions()
    
    if not transactions:
        print("No transactions found in the database.")
        return
    
    # Calculate basic statistics
    total_transactions = len(transactions)
    
    # Get date range
    dates = [t.date for t in transactions if hasattr(t, 'date') and t.date]
    earliest_date = min(dates) if dates else "N/A"
    latest_date = max(dates) if dates else "N/A"
    
    # Count by account
    accounts = {}
    for t in transactions:
        if hasattr(t, 'account_number') and t.account_number:
            accounts[t.account_number] = accounts.get(t.account_number, 0) + 1
    
    # Print statistics
    print("\n=== Database Statistics ===")
    print(f"Total transactions: {total_transactions}")
    print(f"Earliest date: {earliest_date}" if earliest_date != "N/A" else "No dates found")
    print(f"Latest date: {latest_date}" if latest_date != "N/A" else "No dates found")
    
    if accounts:
        print("\nTransactions by account:")
        for account, count in accounts.items():
            print(f"  - {account}: {count}")
    else:
        print("\nNo account information available.")

def delete_all_transactions(_) -> None:
    """Delete all transactions from the database."""
    confirm = input("Are you sure you want to delete ALL transactions? This cannot be undone. (y/n): ")
    if confirm.lower() == 'y':
        repo = SQLiteTransactionRepository()
        count = repo.delete_all_transactions()
        print(f"Deleted {count} transactions.")
    else:
        print("Operation cancelled.")

def create_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(description="Database query tool")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Common arguments
    common_args = argparse.ArgumentParser(add_help=False)
    common_args.add_argument("--start-date", help="Start date (YYYY-MM-DD)")
    common_args.add_argument("--end-date", help="End date (YYYY-MM-DD)")
    common_args.add_argument("--limit", type=int, help="Limit number of results")
    common_args.add_argument("--gui", action="store_true", help="Use GUI file dialog (requires PySide6)")
    
    # List command
    list_parser = subparsers.add_parser(
        "list", 
        parents=[common_args],
        help="List transactions"
    )
    list_parser.set_defaults(func=list_transactions)
    
    # Export command
    export_parser = subparsers.add_parser(
        "export", 
        parents=[common_args],
        help="Export transactions to CSV"
    )
    export_parser.set_defaults(func=export_transactions)
    
    # Stats command
    stats_parser = subparsers.add_parser("stats", help="Show database statistics")
    stats_parser.set_defaults(func=show_stats)
    
    # Delete command
    delete_parser = subparsers.add_parser(
        "delete-all", 
        help="Delete all transactions"
    )
    delete_parser.set_defaults(func=delete_all_transactions)
    
    # Add version
    parser.add_argument(
        '-v', '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )
    
    return parser

def main() -> None:
    """Main entry point for the CLI."""
    try:
        parser = create_parser()
        args = parser.parse_args()
        
        if hasattr(args, 'func'):
            args.func(args)
        else:
            parser.print_help()
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
