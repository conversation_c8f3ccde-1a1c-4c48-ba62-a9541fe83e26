# Column Management Design

## Overview

This document outlines the design for a comprehensive column management system in the FlatMate application. It addresses the need for a centralized approach to column definitions, metadata, and operations across the application.

## Core Requirements

1. **Single Source of Truth**: All column definitions in one place
2. **Clear Purpose Separation**: Explicit grouping of columns by purpose
3. **Discoverable Metadata**: Easy access to column properties and metadata
4. **User Preference Support**: Handle custom display names and formats
5. **Consistent API**: Standard methods for column operations

## Column Categories

### 1. Source Columns
Columns found in raw bank statements and used during import:
- DATE, DETAILS, AMOUNT, BALANCE, etc.
- Special columns like EMPTY_COLUMN (for standardization only)

### 2. Display Columns
Columns shown to users in the UI:
- Core transaction data
- Categorization data
- User-customizable names and formats

### 3. Categorization Columns
User-defined categorization fields:
- CATEGORY, TAGS, NOTES

### 4. System Columns
For internal use only:
- ID (SQL database only)
- UNIQUE_ID (for transactions without balance)

## Implementation Approach

### Column Definition with Metadata

We'll use a hybrid approach combining enums and dataclasses for maximum discoverability and structure:

```python
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Any, Optional

@dataclass
class ColumnMetadata:
    """Metadata for a standard column"""
    display_name: str
    used_in: List[str]  # ['source', 'display', 'categorize', 'system']
    editable: bool = False
    width: Optional[int] = None
    description: Optional[str] = None

class ColumnUsage(Enum):
    """Usage categories for columns"""
    SOURCE = "source"      # Found in raw bank statements
    DISPLAY = "display"    # Shown in the UI
    CATEGORIZE = "categorize"  # Used for categorization
    SYSTEM = "system"      # Internal use only

class StandardColumns(Enum):
    """Standardized column names with metadata"""
    DATE = ColumnMetadata(
        display_name="Date",
        used_in=["source", "display", "categorize"],
        editable=False,
        width=12,
        description="Transaction date"
    )
    
    DETAILS = ColumnMetadata(
        display_name="Details",
        used_in=["source", "display", "categorize"],
        editable=False,
        width=40,
        description="Transaction details/description"
    )
    
    AMOUNT = ColumnMetadata(
        display_name="Amount",
        used_in=["source", "display", "categorize"],
        editable=False,
        width=12,
        description="Transaction amount"
    )
    
    UNIQUE_ID = ColumnMetadata(
        display_name="Unique Id",
        used_in=["source", "system", "display"],  # Can be displayed if data exists
        editable=False,
        width=15,
        description="Unique transaction identifier used by some banks"
    )
    
    # More columns...
    
    @property
    def db_name(self):
        """Get database column name"""
        return self.name.lower()
    
    @property
    def metadata(self) -> ColumnMetadata:
        """Get column metadata"""
        return self.value
    
    @classmethod
    def get_columns_by_usage(cls, usage: str) -> List['StandardColumns']:
        """Get all columns for a specific usage category"""
        return [col for col in cls if usage in col.value.used_in]
    
    @classmethod
    def from_display_name(cls, display_name: str) -> Optional['StandardColumns']:
        """Get column by display name"""
        for col in cls:
            if col.value.display_name == display_name:
                return col
        return None
```

### Column Manager Service

A dedicated service for column operations:

```python
class ColumnManager:
    """
    Centralized service for column management operations.
    """
    def __init__(self, user_preferences=None):
        self._standard_columns = StandardColumns
        self.user_preferences = user_preferences or {}
    
    def get_columns_by_usage(self, usage):
        """Get columns for a specific usage category"""
        return StandardColumns.get_columns_by_usage(usage)
    
    def get_source_columns(self):
        """Get columns used in source data (bank statements)"""
        return self.get_columns_by_usage("source")
    
    def get_display_columns(self):
        """Get columns intended for display"""
        return self.get_columns_by_usage("display")
    
    def get_categorization_columns(self):
        """Get columns used for categorization"""
        return self.get_columns_by_usage("categorize")
    
    def get_system_columns(self):
        """Get columns for internal system use"""
        return self.get_columns_by_usage("system")
        
    def get_display_columns_with_data(self, df):
        """
        Get display columns that actually contain data in the given DataFrame.
        
        Args:
            df: DataFrame to check for data presence
            
        Returns:
            List of columns that are marked for display AND have non-null data
        """
        display_cols = self.get_display_columns()
        cols_with_data = []
        
        for col in display_cols:
            col_name = col.value.display_name
            db_name = col.db_name
            
            # Check if column exists in DataFrame (either as display or db name)
            if col_name in df.columns:
                # Check if it has any non-null values
                if not df[col_name].isna().all():
                    cols_with_data.append(col)
            elif db_name in df.columns:
                # Check if it has any non-null values
                if not df[db_name].isna().all():
                    cols_with_data.append(col)
        
        return cols_with_data
    
    def convert_columns(self, df, to_format='display'):
        """Convert DataFrame columns between formats"""
        if to_format == 'display':
            # Convert from db to display names
            mapping = {col.db_name: col.value.display_name for col in StandardColumns}
        elif to_format == 'db':
            # Convert from display to db names
            mapping = {col.value.display_name: col.db_name for col in StandardColumns}
        else:
            return df.copy()
            
        # Apply mapping to columns that exist in the DataFrame
        rename_dict = {old: new for old, new in mapping.items() 
                      if old in df.columns}
        if rename_dict:
            return df.rename(columns=rename_dict)
        return df.copy()
    
    def apply_user_preferences(self, df):
        """Apply user display preferences to a DataFrame"""
        if not self.user_preferences:
            return df.copy()
            
        # Apply custom column names if defined
        custom_names = self.user_preferences.get('custom_display_names', {})
        if custom_names:
            rename_dict = {col: custom_names.get(col, col) for col in df.columns 
                          if col in custom_names}
            if rename_dict:
                df = df.rename(columns=rename_dict)
        
        # Apply column order if defined
        column_order = self.user_preferences.get('column_order', [])
        if column_order:
            available_cols = [c for c in column_order if c in df.columns]
            other_cols = [c for c in df.columns if c not in column_order]
            df = df[available_cols + other_cols]
            
        return df
```

### Enhanced DBIOService

The DBIOService leverages the ColumnManager for all column operations:

```python
class DBIOService:
    """High-level service for database I/O operations."""
    
    def __init__(self, repo=None, column_manager=None):
        """Initialize the DB I/O Service."""
        self.repo = repo or SQLiteTransactionRepository()
        self.column_manager = column_manager or ColumnManager()
    
    def get_transactions_df(self, **kwargs):
        """
        Get transactions as a DataFrame with configurable options.
        
        Args:
            col_format: str = 'display' - Column format ('display', 'db', 'raw')
            col_set: str = 'default' - Column set ('default', 'all', 'core_only')
            apply_preferences: bool = True - Whether to apply user preferences
            only_columns_with_data: bool = True - Whether to filter out columns with no data
        """
        col_format = kwargs.pop('col_format', 'display')
        col_set = kwargs.pop('col_set', 'default')
        apply_preferences = kwargs.pop('apply_preferences', True)
        only_columns_with_data = kwargs.pop('only_columns_with_data', True)
        
        # Get transactions based on filters
        transactions = self.list_transactions(**kwargs)
        
        if not transactions:
            # Return empty DataFrame with appropriate columns
            columns = self._get_columns_for_set(col_set, col_format)
            return pd.DataFrame(columns=columns)
        
        # Convert transactions to DataFrame
        df = pd.DataFrame([t.__dict__ for t in transactions])
        
        # Apply column set filtering
        df = self._filter_columns_by_set(df, col_set)
        
        # Apply column format conversion
        if col_format != 'raw':
            df = self.column_manager.convert_columns(df, col_format)
        
        # Filter out display columns with no data if requested
        if only_columns_with_data and col_format == 'display':
            display_cols = self.column_manager.get_display_columns_with_data(df)
            display_col_names = [col.value.display_name for col in display_cols]
            df = df[[col for col in df.columns if col in display_col_names]]
        
        # Apply user preferences if requested
        if apply_preferences:
            df = self.column_manager.apply_user_preferences(df)
        
        return df
    
    def dump_raw_db_content(self, table_name: str = "transactions", 
                           limit: int = 100, 
                           output_format: str = "dataframe"):
        """
        Dump raw database content for debugging/testing purposes.
        
        Args:
            table_name: Name of the table to dump
            limit: Maximum number of rows to return (0 for all)
            output_format: 'dataframe' or 'json' or 'csv'
        
        Returns:
            Raw database content in the requested format
        """
        # Get raw data from repository
        query = f"SELECT * FROM {table_name}"
        if limit > 0:
            query += f" LIMIT {limit}"
        
        raw_data = self.repo.execute_raw_query(query)
        
        # Convert to DataFrame
        df = pd.DataFrame(raw_data)
        
        # Return in requested format
        if output_format == "dataframe":
            return df
        elif output_format == "json":
            return df.to_json(orient="records", indent=2)
        elif output_format == "csv":
            return df.to_csv(index=False)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
```

## Module Organization

All data-related services will be consolidated into a unified `core.data_services` module:

```
core/
  data_services/
    __init__.py
    column_manager.py       # Column management utilities
    standard_columns.py     # Enhanced StandardColumns definition
    user_preferences.py     # User display preferences
    date_service.py         # Date handling utilities
    db_io_service.py        # Database I/O operations
    sql/                    # SQL-specific implementations
      repository.py         # Base repository interface
      sqlite_repository.py  # SQLite implementation
```

## Benefits

1. **Discoverability**: Column metadata is easily accessible through IDE completion and introspection
2. **Flexibility**: Clear separation of concerns between canonical definitions and user preferences
3. **Consistency**: All modules reference the same column definitions
4. **Maintainability**: Centralized column management reduces duplication
5. **Debugging**: Raw DB content dump makes it easier to diagnose issues

## Implementation Strategy

1. **Phase 1: Enhanced Column Definitions**
   - Implement ColumnMetadata dataclass
   - Update StandardColumns with metadata
   - Add purpose-based column grouping

2. **Phase 2: Column Manager**
   - Implement ColumnManager service
   - Add column conversion methods
   - Add user preference handling

3. **Phase 3: DBIOService Integration**
   - Update DBIOService to use ColumnManager
   - Add raw DB content dump method
   - Implement column set filtering

4. **Phase 4: Module Consolidation**
   - Move all services to core.data_services
   - Update imports across the codebase
   - Add comprehensive tests

## Conclusion

This design provides a comprehensive approach to column management in the FlatMate application. By centralizing column definitions and operations, we can ensure consistency, improve maintainability, and make it easier to add new features in the future. The hybrid approach using enums and dataclasses provides both structure and discoverability, while the ColumnManager service provides a clean API for column operations.
