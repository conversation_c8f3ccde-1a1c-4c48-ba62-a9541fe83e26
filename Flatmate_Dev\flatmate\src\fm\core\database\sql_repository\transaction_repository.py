"""
Transaction repository interface.
Defines the contract for transaction data storage.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, fields
from datetime import datetime
from typing import Dict, List, Optional, Any


@dataclass
class ImportResult:
    """Result of an import operation."""
    added_count: int = 0
    duplicate_count: int = 0
    error_count: int = 0
    errors: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


@dataclass
class Transaction:
    """Transaction data model, based on StandardColumns."""
    # System Fields
    id: Optional[int] = None
    import_date: Optional[datetime] = None
    modified_date: Optional[datetime] = None

    # Core Transaction Identifiers
    date: Optional[datetime] = None
    details: Optional[str] = ""
    amount: Optional[float] = None
    balance: Optional[float] = None
    account: Optional[str] = ""
    
    # Unique Transaction Identifier
    unique_id: Optional[str] = ""
    
    # Amount Columns
    credit_amount: Optional[float] = None
    debit_amount: Optional[float] = None

    # Source Info
    source_filename: Optional[str] = ""
    source_bank: Optional[str] = ""
    
    # This Party (TP) Details
    payment_type: Optional[str] = ""
    tp_ref: Optional[str] = ""
    tp_part: Optional[str] = ""
    tp_code: Optional[str] = ""

    # Other Party (OP) Details
    op_ref: Optional[str] = ""
    op_part: Optional[str] = ""
    op_code: Optional[str] = ""
    op_name: Optional[str] = ""
    op_account: Optional[str] = ""

    # Categorization and User Fields
    category: Optional[str] = ""
    tags: Optional[str] = ""
    notes: Optional[str] = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert transaction to a dictionary, handling None and datetime."""
        data = {}
        for f in fields(self):
            value = getattr(self, f.name)
            if isinstance(value, datetime):
                data[f.name] = value.isoformat() if value else None
            else:
                data[f.name] = value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Transaction':
        """Create transaction from a dictionary."""
        
        def _parse_date(d):
            if d and isinstance(d, str):
                try:
                    return datetime.fromisoformat(d.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    return None
            return d

        init_data = {}
        for f in fields(cls):
            if f.name in data:
                value = data[f.name]
                # Check if the field is for a datetime
                if f.type is Optional[datetime] or f.type is datetime:
                    init_data[f.name] = _parse_date(value)
                else:
                    init_data[f.name] = value
        
        return cls(**init_data)


class TransactionRepository(ABC):
    """Interface for transaction data storage."""
    
    @abstractmethod
    def add_transactions(self, transactions: List[Transaction]) -> ImportResult:
        """
        Add new transactions to the repository.
        
        Args:
            transactions: List of transactions to add
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        pass
    
    @abstractmethod
    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """
        Retrieve transactions matching the filters.
        
        Args:
            filters: Dictionary of filter criteria
            
        Returns:
            List of matching transactions
        """
        pass
    
    @abstractmethod
    def update_transaction(self, transaction_id: Optional[int], data: Dict) -> bool:
        """
        Update a specific transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            data: Dictionary of fields to update
            
        Returns:
            True if update was successful
        """
        pass
    
    @abstractmethod
    def delete_transaction(self, transaction_id: int) -> bool:
        """
        Mark a transaction as deleted.
        
        Args:
            transaction_id: ID of the transaction to delete
            
        Returns:
            True if deletion was successful
        """
        pass
        
    @abstractmethod
    def delete_all_transactions(self) -> int:
        """
        Mark all transactions as deleted.
        
        Returns:
            Number of transactions deleted
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict:
        """
        Get statistics about the stored transactions.
        
        Returns:
            Dictionary with transaction statistics
        """
        pass
        
    @abstractmethod
    def add_transactions_from_df(self, df, source_file: Optional[str] = None) -> ImportResult:
        """
        Add new transactions from a pandas DataFrame.
        
        Args:
            df: Pandas DataFrame containing transaction data
            source_file: Optional source file path for reference
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        pass
