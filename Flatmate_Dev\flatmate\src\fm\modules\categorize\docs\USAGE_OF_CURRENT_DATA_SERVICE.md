# Current Data Service Usage in Categorization Module

## Overview
This document outlines how the categorization module interacts with the application's data service for database operations. The current implementation represents an initial sketch of the data access patterns.

## Service Initialization

The data service is initialized directly in the `CategorizePresenter`:

```python
# File: fm/modules/categorize/cat_presenter.py

from fm.database_service.service import DataService

class CategorizePresenter:
    def __init__(self, main_window):
        # ...
        self.data_service = DataService()  # Direct instantiation
        # ...
```

## Data Access Patterns

### 1. Reading Data

Transactions are loaded using the `get_transactions` method with optional filters:

```python
# File: fm/modules/categorize/cat_presenter.py

def _handle_load_db(self, filters=None):
    """Load transactions from database with optional filters"""
    try:
        # Get transactions using the service
        transactions = self.data_service.get_transactions(
            start_date=filters.get('start_date'),
            end_date=filters.get('end_date'),
            account_number=filters.get('account_number')
        )
        # Convert to DataFrame for the view
        self._original_df = pd.DataFrame([t.__dict__ for t in transactions])
        # Update view
        self.view.update_transactions(self._original_df)
    except Exception as e:
        log(f"Error loading transactions: {str(e)}", level="error")
```

### 2. Updating Data

#### a) Updating Tags
```python
# File: fm/modules/categorize/cat_presenter.py

def _handle_tags_updated(self, transaction_id, tags):
    """Handle tag updates from the view"""
    if self._original_df is not None:
        # Update local DataFrame
        idx = self._original_df['id'] == transaction_id
        if not idx.empty:
            self._original_df.loc[idx, 'tags'] = tags
            self._modified = True
            
            # Update in database via service
            success = self.data_service.update_transaction_tags(
                transaction_id, 
                tags
            )
            if not success:
                log("Failed to update transaction tags", level="error")
```

#### b) General Updates
```python
# File: fm/modules/categorize/cat_presenter.py

def update_transaction_field(self, transaction_id, field, value):
    """Update a single field of a transaction"""
    try:
        success = self.data_service.update_transaction(
            transaction_id,
            {field: value}
        )
        if success and self._original_df is not None:
            # Update local cache
            idx = self._original_df['id'] == transaction_id
            self._original_df.loc[idx, field] = value
        return success
    except Exception as e:
        log(f"Error updating {field}: {str(e)}")
        return False
```

## Data Flow

1. **Read Operations**:
   - Presenter (`CategorizePresenter._handle_load_db`) calls `data_service.get_transactions()`
   - Returns list of `Transaction` objects
   - Converted to DataFrame for UI binding

2. **Write Operations**:
   - UI triggers update (e.g., tag change in `TransactionViewPanel`)
   - Presenter (`_handle_tags_updated`) updates local DataFrame
   - Presenter calls appropriate `data_service` method
   - Service (`DataService`) updates database via repository

## Current Implementation Notes

1. **Direct Service Instantiation**:
   - `CategorizePresenter` directly instantiates `DataService`
   - No dependency injection or service locator pattern
   - Makes testing and mocking more difficult

2. **Caching Strategy**:
   - Maintains a local DataFrame cache (`_original_df`)
   - All updates first modify the cache, then sync to database
   - No invalidation strategy for cache consistency

3. **Error Handling**:
   - Basic try/except blocks in presenter methods
   - Logs errors but has limited recovery
   - No retry mechanism for transient failures

4. **Transaction Management**:
   - Each update is atomic
   - No explicit transaction batching
   - No rollback capability for multi-operation updates

## Column Name Handling

1. **Standard Columns**:
   - Accessed via `StandardColumns` enum (e.g., `StandardColumns.DATE.db_name`)
   - Defined in `fm.core.standards.fm_standard_columns`
   - Used for core transaction fields

2. **Extended Columns**:
   - Direct string literals (e.g., `"category"`, `"tags"`)
   - Used in `SQLiteTransactionRepository.update_transaction()`
   - No compile-time checking
   - Risk of typos

## Recommendations for Improvement

1. **Dependency Injection**:
   ```python
   # In CategorizePresenter.__init__
   def __init__(self, main_window, data_service: DataService):
       self.data_service = data_service
   ```

2. **Repository Pattern**:
   - Create a dedicated repository for categorization
   - Encapsulate category-specific queries
   - Separate from generic transaction operations

3. **Type Safety**:
   - Use enums or dataclasses for category values
   - Add runtime validation

4. **Batch Updates**:
   - Support updating multiple transactions in a single call
   - Reduce database round-trips

## Current Limitations

1. **Tight Coupling**:
   - Presenter knows about concrete service implementation
   - Difficult to swap implementations

2. **Limited Abstraction**:
   - Business logic mixed with data access
   - No clear separation of concerns

3. **No Validation**:
   - Category values not validated
   - No business rules enforcement

4. **Performance**:
   - No query optimization
   - No pagination for large datasets
