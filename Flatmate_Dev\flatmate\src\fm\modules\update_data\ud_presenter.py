#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.
"""

import os
from pathlib import Path

import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import LogLevel, log, log_this

# Import the data service for database operations
from ...core.data_services import CSVToTransactionConverter

# ?from ...gui.components.panel_components import RightPanelManager
from ...gui.services.info_bar_service import InfoBarService
from ._view.ud_view import UpdateDataView
from .config.ud_config import ud_config
from .utils.option_types import SaveOptions, SourceOptions
from .services.events import UpdateDataEvents
from .utils.dw_director import dw_director


class UpdateDataPresenter:
    """Presenter for the Update Data module."""

    @log_this(LogLevel.INFO)
    def __init__(self, main_window):
        """Initialize the Update Data presenter.

        Args:
            main_window: The main window instance
        """
        self.view = UpdateDataView(main_window)
        self.main_window = main_window

        # Get the InfoBarService instance
        self.info_bar_service = InfoBarService.get_instance()

        # State tracking
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary
        self.csv_converter = CSVToTransactionConverter()  # For database updates

        self._connect_signals()

    def _connect_signals(self):
        """Connect view signals to handlers."""
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.source_select_requested.connect(self._handle_source_select)
        self.view.save_select_requested.connect(self._handle_save_select)
        self.view.source_option_changed.connect(self._handle_source_option_change)
        self.view.save_option_changed.connect(self._handle_save_option_change)
        self.view.process_clicked.connect(self._handle_process)
        self.view.update_database_changed.connect(self._handle_update_database_change)

        # Initialize save select button state based on default save option
        initial_save_option = self.view.get_save_option()
        is_same_as_source = initial_save_option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        log("Signals connected", "d")

        # Subscribe to Update Data events
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name,
            self._on_processing_started,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name,
            self._on_processing_stats,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self._on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self._on_processing_completed,
        )

    def initialize(self):
        """Initialize the Update Data module."""
        # Set up view in main window
        self.view.setup_in_main_window(self.main_window)
        log("ud_presenter called self.view.setup_in_main_window", "d")

        # Explicitly control panel visibility - presenter is responsible for UI state
        self.main_window.show_left_panel()
        # ?self.main_window.show_right_panel()

        # Show the InfoBar with a welcome message when the module is initialized
        self.info_bar_service.show()
        self.info_bar_service.publish_message(
            "Ready to process files. Add files and select save location to begin."
        )

        # Check for existing master file
        recent_masters = ud_config.get_value("recent_masters", default=[])
        if recent_masters:
            master_path = recent_masters[-1]  # Get most recent master
            if os.path.exists(master_path):  # Ensure path exists
                try:
                    master_df = pd.read_csv(
                        str(master_path)
                    )  # Convert Path to string for pandas
                    self.view.display_master_csv(master_df)
                    self.view.set_exit_mode()  # Set to exit mode since we're displaying existing data
                except Exception as e:
                    log(f"Error loading master file: {e}", "e")
                    self.view.display_welcome()  # Fall back to welcome screen
            else:
                self.view.display_welcome()
        else:
            self.view.display_welcome()

    def _handle_source_select(self, selection_type: str):
        """Handle source selection request."""
        print(f"[DEBUG] _handle_source_select called with: {selection_type}")
        
        if selection_type == SourceOptions.SELECT_FOLDER.value:
            print(f"[DEBUG] Folder selection starting...")
            
            folder = self.view.show_folder_dialog(
                "Select Source Folder",
                str(ud_config.get_value("last_source_dir", default=str(Path.home()))),
            )
            if folder:
                # Get allowed extensions from config
                extensions = ud_config.get_allowed_file_extensions()
                print(f"[DEBUG] Looking for files with extensions: {extensions}")
                
                # Get all files with allowed extensions (case insensitive)
                file_paths = []
                for file_name in os.listdir(folder):
                    if any(file_name.lower().endswith(ext.lower()) for ext in extensions):
                        full_path = os.path.join(folder, file_name)
                        file_paths.append(full_path)
                        print(f"[DEBUG] Found file: {file_name}")

                if not file_paths:
                    ext_list = ", ".join(extensions)
                    self.view.show_error(f"No supported files found in selected folder. Looking for: {ext_list}")
                    return

                # Create a dictionary with source information
                self.selected_source = {
                    "type": "folder",
                    "path": folder,
                    "file_paths": file_paths,
                }

                ud_config.set_value("last_source_dir", folder)
                self.info_bar_service.publish_message(f"Source selected: {folder}")

                # Display the selected source in the view
                self.view.display_selected_source(self.selected_source)

                # If save location is set to same as source, update it
                if self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
                    self.save_location = folder
                    self.view.set_save_path("Same as source...")
                    self.info_bar_service.publish_message(f"Save location: {folder}")

        else:  # SourceOptions.SELECT_FILES
            print("[DEBUG] File selection dialog starting...")
            
            # Get allowed extensions from config
            extensions = ud_config.get_allowed_file_extensions()
            
            # Build simple filter string
            patterns = " ".join(f"*{ext}" for ext in extensions)
            filter_str = f"Data Files ({patterns})"
            
            print(f"[DEBUG] Built filter string: {filter_str}")
            
            # Debug: Log what we're doing
            from ...core.services.logger import log
            log(f"File dialog - Extensions: {extensions}, Filter: {filter_str}", "DEBUG")
            
            files = self.view.show_files_dialog(
                "Select Source Files",
                str(ud_config.get_value("last_source_dir", default=str(Path.home()))),
                filter_str,
            )
            if files:
                # Filter files by allowed extensions
                allowed_files = [
                    f for f in files 
                    if any(f.lower().endswith(ext.lower()) for ext in extensions)
                ]
                if not allowed_files:
                    self.view.show_error(f"No supported files selected. Allowed: {', '.join(extensions)}")
                    return

                source_dir = os.path.dirname(allowed_files[0])
                self.selected_source = {
                    "type": "files",
                    "file_paths": allowed_files,
                }

                ud_config.set_value("last_source_dir", source_dir)
                self.info_bar_service.publish_message(f"Source selected: {source_dir}")

                # If save location is set to same as source, update it
                if self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
                    self.save_location = source_dir
                    self.view.set_save_path("Same as source...")
                    self.info_bar_service.publish_message(
                        f"Save location: {source_dir}"
                    )

    def _handle_save_select(self):
        """Handle save location selection."""
        folder = self.view.show_folder_dialog(
            "Select Save Location",
            str(ud_config.get_value("last_save_dir", default=str(Path.home()))),
        )
        if folder:
            self.save_location = folder
            ud_config.set_value("last_save_dir", folder)
            self.view.set_save_path(folder)
            self.info_bar_service.publish_message(f"Save location: {folder}")

    def _handle_source_option_change(self, option: str):
        """Handle source option change."""
        # Reset selected source when option changes
        self.selected_source = None
        self.info_bar_service.clear()
        self.info_bar_service.publish_message(f"Selected source option: {option}")

    def _handle_save_option_change(self, option: str):
        """Handle save location option change."""
        # Disable save select button when using same as source
        is_same_as_source = option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        if is_same_as_source:
            # Update the save location label to indicate "Same as source..."
            self.view.set_save_path("Same as source...")

            if self.selected_source:
                # If we have a selected source, use its directory
                if self.selected_source["type"] == "folder":
                    self.save_location = self.selected_source["path"]
                else:  # files
                    self.save_location = os.path.dirname(
                        self.selected_source["file_paths"][0]
                    )
                self.info_bar_service.publish_message(
                    f"Save location: {self.save_location}"
                )
        else:
            # Reset save location if not using same as source
            self.save_location = None
            self.view.set_save_path("")  # Clear the save path label
            self.info_bar_service.publish_message("")

    def _handle_update_database_change(self, checked: bool):
        """Handle database update checkbox state change."""
        status = "enabled" if checked else "disabled"
        self.info_bar_service.publish_message(f"Database updates {status}")

    def _handle_process(self):
        """Handle process button click."""
        # Make sure the InfoBar is visible
        self.info_bar_service.show()

        if not self.selected_source:
            error_msg = "Please select source files first"
            self.view.show_error(error_msg)
            self.info_bar_service.publish_message(error_msg)
            return

        if not self.save_location:
            error_msg = "Please select a save location"
            self.view.show_error(error_msg)
            self.info_bar_service.publish_message(error_msg)
            return

        try:
            # Create job sheet for the director
            job_sheet = {
                "filepaths": self.selected_source["file_paths"],
                "save_folder": self.save_location,
                "update_database": self.view.get_update_database(),
            }

            # Show processing status in InfoBar
            self.info_bar_service.show()
            self.info_bar_service.publish_message(
                f"Processing {len(job_sheet['filepaths'])} files..."
            )

            # Process files using the director
            result = dw_director(job_sheet)

            if result.get("status") == "success":
                # Update InfoBar with success message
                self.info_bar_service.publish_message(
                    f"Successfully processed {len(job_sheet['filepaths'])} files"
                )

                # Also update the info widget for consistency
                self.info_bar_service.publish_message(
                    f"Successfully processed {len(job_sheet['filepaths'])} files"
                )

                # Update master file history
                master_path = result.get("output_path")
                if master_path:
                    recent_masters = ud_config.get_value("recent_masters", default=[])
                    if master_path not in recent_masters:
                        recent_masters.append(master_path)
                        if len(recent_masters) > 10:  # Keep only last 10
                            recent_masters.pop(0)
                        ud_config.set_value("recent_masters", recent_masters)

                # Check if database was updated and show results
                if self.view.get_update_database() and "database_updated" in result.get(
                    "details", {}
                ):
                    # Get database update stats from the result
                    added_count = result["details"].get("added_count", 0)
                    duplicate_count = result["details"].get("duplicate_count", 0)

                    # Update InfoBar with database update status
                    self.info_bar_service.publish_message(
                        f"Successfully processed {len(job_sheet['filepaths'])} files. "
                        f"Database updated: {added_count} added, {duplicate_count} duplicates"
                    )
            else:
                error_msg = result.get("message", "Unknown error occurred")
                # Update InfoBar with error message
                self.info_bar_service.publish_message(f"Error: {error_msg}")

                # Also update the info widget for consistency
                self.info_bar_service.publish_message(f"Error: {error_msg}")
                log(f"Processing error: {error_msg}", "e")

        except Exception as e:
            error_msg = f"Error processing files: {str(e)}"
            # Update InfoBar with error message
            self.info_bar_service.publish_message(f"Error: {error_msg}")

            # Also update the info widget for consistency
            self.info_bar_service.publish_message(f"Error: {error_msg}")
            log(error_msg, "e")

    def request_transition(self, target_view: str):
        """Request transition to another view."""
        self.view.cleanup()
        global_event_bus.publish(Events.VIEW_CHANGE_REQUESTED, target_view)

    def cleanup(self):
        """Clean up before being replaced."""
        # Hide the InfoBar when the module is deactivated
        # This allows other modules to control InfoBar visibility
        self.info_bar_service.hide()

        if self.view:
            self.view.cleanup()

    @log_this()
    def _test_log_decorator(self, x: int) -> int:
        """
        Test function to demonstrate logging decorator

        Args:
            x: Input number

        Returns:
            Squared input number
        """
        return x * x

    @log_this(LogLevel.INFO)
    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_bar_service.publish_message(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass

    def _on_processing_started(self, job_sheet):
        """Handle processing started event."""
        file_count = len(job_sheet.get("filepaths", [])) or 0
        log(f"Processing started for {file_count} files", "info")

        # Update both the InfoBar and the info widget for consistency
        self.info_bar_service.show()
        self.info_bar_service.publish_message(f"Processing {file_count} files...")
        self.info_bar_service.publish_message(f"Processing {file_count} files...")

    def _on_processing_stats(self, stats):
        """Handle processing stats event."""
        log(f"Processing stats: {stats}", "debug")
        total = stats.get("total_files", 0)
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Update progress in the info widget
        self.info_bar_service.publish_message(f"Processing: {processed}/{total} files")

        # Update the InfoBar with current progress
        if total > 0:
            self.info_bar_service.publish_message(
                f"Processing files: {processed}/{total} complete"
            )

    def _on_unrecognized_files(self, unrecognized_files):
        """Handle unrecognized files event."""
        log(f"Unrecognized files detected: {unrecognized_files}", "warning")

        # Update the InfoBar with a warning about unrecognized files
        count = len(unrecognized_files)
        self.info_bar_service.publish_message(
            f"Warning: {count} unrecognized file(s) detected"
        )

        # Also update the info widget with detailed error messages
        for file_info in unrecognized_files:
            self.info_bar_service.publish_message(
                f"Warning: Unrecognized file: {file_info['filepath']}\n"
                f"Reason: {file_info.get('reason', 'Unknown')}"
            )

    def _on_processing_completed(self, result):
        """Handle processing completed event."""
        log("File processing completed", "info")
        stats = result.get("stats", {})
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Get backup stats if available
        backup_stats = result.get("backup_stats", {})
        backed_up = backup_stats.get("backed_up_count", 0)
        skipped = backup_stats.get("skipped_count", 0)

        # Update the InfoBar with completion message
        status_msg = f"Processing complete. {processed} files processed successfully."
        self.info_bar_service.publish_message(status_msg)

        # Build status message with all stats
        status_msg = f"Processing complete. {processed} files processed successfully."

        # Add backup stats if available
        if backed_up > 0 or skipped > 0:
            status_msg += (
                f" {backed_up} files backed up, {skipped} identical files skipped."
            )

        # Add error info if unrecognized files exist
        if unrecognized > 0:
            status_msg += f" {unrecognized} files unrecognized."
            self.info_bar_service.publish_message(f"Error: {status_msg}")
        else:
            self.info_bar_service.publish_message(status_msg)
