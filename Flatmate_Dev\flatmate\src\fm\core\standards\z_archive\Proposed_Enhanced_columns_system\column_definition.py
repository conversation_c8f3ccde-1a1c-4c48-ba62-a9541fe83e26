# * proposed draft *
"""
Proposed draft of Enhanced column definition system for Flatmate application.

This module provides a comprehensive column mapping system that serves as the single
source of truth for all column-related operations across the application.
"""

from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from enum import Enum


@dataclass
class ColumnDefinition:
    """
    Complete definition of a column with all naming variants and metadata.
    
    This class encapsulates all the different ways a column can be referenced:
    - Database storage name
    - Transaction dataclass field name  
    - User-facing display name
    - Configuration and metadata
    """
    
    # Core identifiers
    key: str                    # Internal unique key (e.g., "transaction_details")
    db_name: str               # Database column name (e.g., "details")
    field_name: str            # Transaction dataclass field (e.g., "description")
    
    # Display options
    default_display: str       # Default display name (e.g., "Description")
    short_display: str         # Short display name (e.g., "Desc")
    user_display: Optional[str] = None  # User-customized display name
    
    # Metadata
    data_type: str = "TEXT"    # SQL data type
    is_editable: bool = False  # Can user edit this column?
    is_required: bool = False  # Must be visible?
    default_width: int = 15    # Default column width in characters
    sort_order: int = 0        # Default sort order for display
    
    # Additional metadata
    description: str = ""      # Help text for users
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def display_name(self) -> str:
        """Get the current display name (user preference or default)."""
        return self.user_display or self.default_display
    
    def set_user_display_name(self, name: str):
        """Set a custom user display name."""
        self.user_display = name
    
    def reset_display_name(self):
        """Reset to default display name."""
        self.user_display = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'key': self.key,
            'db_name': self.db_name,
            'field_name': self.field_name,
            'default_display': self.default_display,
            'short_display': self.short_display,
            'user_display': self.user_display,
            'data_type': self.data_type,
            'is_editable': self.is_editable,
            'is_required': self.is_required,
            'default_width': self.default_width,
            'sort_order': self.sort_order,
            'description': self.description,
            'validation_rules': self.validation_rules
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ColumnDefinition':
        """Create from dictionary (for deserialization)."""
        return cls(
            key=data['key'],
            db_name=data['db_name'],
            field_name=data['field_name'],
            default_display=data['default_display'],
            short_display=data['short_display'],
            user_display=data.get('user_display'),
            data_type=data.get('data_type', 'TEXT'),
            is_editable=data.get('is_editable', False),
            is_required=data.get('is_required', False),
            default_width=data.get('default_width', 15),
            sort_order=data.get('sort_order', 0),
            description=data.get('description', ''),
            validation_rules=data.get('validation_rules', {})
        )


class DataType(Enum):
    """Standard data types for columns."""
    TEXT = "TEXT"
    INTEGER = "INTEGER"
    REAL = "REAL"
    BOOLEAN = "BOOLEAN"
    DATE = "DATE"
    DATETIME = "DATETIME"
    JSON = "JSON"


class ColumnRole(Enum):
    """Roles that columns can play in the system."""
    IDENTIFIER = "identifier"      # Primary keys, unique IDs
    CORE_DATA = "core_data"       # Essential transaction data
    METADATA = "metadata"         # Import dates, source info
    USER_DATA = "user_data"       # Tags, categories, notes
    CALCULATED = "calculated"     # Derived fields
    SYSTEM = "system"            # Internal system fields


# Utility functions for working with column definitions
def create_core_column(
    key: str,
    db_name: str,
    field_name: str,
    display_name: str,
    data_type: DataType = DataType.TEXT,
    width: int = 15,
    sort_order: int = 0,
    required: bool = False
) -> ColumnDefinition:
    """Helper to create a core column definition."""
    return ColumnDefinition(
        key=key,
        db_name=db_name,
        field_name=field_name,
        default_display=display_name,
        short_display=display_name[:4] if len(display_name) > 4 else display_name,
        data_type=data_type.value,
        is_required=required,
        default_width=width,
        sort_order=sort_order,
        description=f"Core transaction field: {display_name}"
    )


def create_user_column(
    key: str,
    db_name: str,
    field_name: str,
    display_name: str,
    width: int = 20,
    sort_order: int = 100,
    editable: bool = True
) -> ColumnDefinition:
    """Helper to create a user-editable column definition."""
    return ColumnDefinition(
        key=key,
        db_name=db_name,
        field_name=field_name,
        default_display=display_name,
        short_display=display_name[:4] if len(display_name) > 4 else display_name,
        data_type=DataType.TEXT.value,
        is_editable=editable,
        default_width=width,
        sort_order=sort_order,
        description=f"User-editable field: {display_name}"
    )


def create_metadata_column(
    key: str,
    db_name: str,
    field_name: str,
    display_name: str,
    data_type: DataType = DataType.TEXT,
    width: int = 15,
    sort_order: int = 200
) -> ColumnDefinition:
    """Helper to create a metadata column definition."""
    return ColumnDefinition(
        key=key,
        db_name=db_name,
        field_name=field_name,
        default_display=display_name,
        short_display=display_name[:4] if len(display_name) > 4 else display_name,
        data_type=data_type.value,
        is_editable=False,
        default_width=width,
        sort_order=sort_order,
        description=f"Metadata field: {display_name}"
    )
